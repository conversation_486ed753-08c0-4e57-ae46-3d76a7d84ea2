<template>
  <div>
    <el-card class="box-card">
      <div class="form">
        <div class="form-content">
          <el-form ref="form" :model="formItem" :rules="rules" label-width="120px" :disabled="setting && setting.opType !== '-1'">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="处置部门" prop="handleDept">
                  <dept-select v-model="formItem.handleDept" :parent-dept-id="deptData.deptId" :is-current="false" :is-expand-all="false" :is-all-data="true" @selected="handleDeptInput" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处置人" prop="handleUser">
                  <el-select size="small" v-model="formItem.handleUser" filterable :filter-method="handleUserFilter" @visible-change="handleUserVisibleChange" :default-first-option="true" placeholder="请选择" :clearable="true" @change="userSelected">
                    <el-option
                      v-for="item in handleOption"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId">
                      <span style="float: left">{{ item.nickName }}</span>
                      <span style="float: right; color: #8492a6;">{{ item.userName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="系统名称">
                  <el-input v-model="formItem.assetName" @focus="applicationDialog = true"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="登录地址">
                  <el-input v-model="formItem.loginUrl"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="责任人">
                  <el-select size="small" v-model="formItem.manager" filterable :filter-method="managerUserFilter" :default-first-option="true" placeholder="请选择" :clearable="true">
                    <el-option
                      v-for="item in managerOption"
                      :key="item.userId"
                      :label="item.nickName"
                      :value="item.userId">
                      <span style="float: left">{{ item.nickName }}</span>
                      <span style="float: right; color: #8492a6;">{{ item.userName }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="formItem.phone"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="form-remove" v-if="setting && setting.opType === '-1'">
          <i class="el-icon-delete" style="color: #f56c6c;cursor: pointer;" @click="removeTarget(curIndex)"></i>
        </div>
      </div>
      <div class="event-list" v-if="formItem && formItem.eventData">
        <el-tabs v-model="eventActiveName" class="event-list-tabs" @tab-click="eventTabClick">
          <el-tab-pane :label="'IP漏洞事件'+'('+getEventSize(formItem.eventData['type1'])+')'" key="1" name="1">
            <div class="event_list" ref="event_list">
              <selected-event-list ref="eventList1" :is-from-event="isFromEvent" :event-ids.sync="formItem.eventData['type1']" :setting="setting" :threatenDict="threatenDict" :currentEventType="1" :severityOptions="severityOptions" @handle="eventHandle"></selected-event-list>
              <el-button v-if="setting && setting.opType === '-1'" type="text" style="width: 100%" icon="el-icon-plus" @click="selectEventClick">选择事件</el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="'应用漏洞事件'+'('+getEventSize(formItem.eventData['type2'])+')'" key="2" name="2">
            <div class="event_list" ref="event_list">
              <selected-event-list ref="eventList2" :is-from-event="isFromEvent" :event-ids.sync="formItem.eventData['type2']" :setting="setting" :threatenDict="threatenDict" :currentEventType="2" :severityOptions="severityOptions" @handle="eventHandle"></selected-event-list>
              <el-button v-if="setting && setting.opType === '-1'" type="text" style="width: 100%" icon="el-icon-plus" @click="selectEventClick">选择事件</el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="'威胁事件'+'('+getEventSize(formItem.eventData['type3'])+')'" key="3" name="3">
            <div class="event_list" ref="event_list">
              <selected-event-list ref="eventList3" :is-from-event="isFromEvent" :event-ids.sync="formItem.eventData['type3']" :setting="setting" :threatenDict="threatenDict" :currentEventType="3" :severityOptions="severityOptions" @handle="eventHandle"></selected-event-list>
              <el-button v-if="setting && setting.opType === '-1'" type="text" style="width: 100%" icon="el-icon-plus" @click="selectEventClick">选择事件</el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane :label="'弱口令'+'('+getEventSize(formItem.eventData['type4'])+')'" key="4" name="4">
            <div class="event_list" ref="event_list">
              <selected-event-list ref="eventList4" :is-from-event="isFromEvent" :event-ids.sync="formItem.eventData['type4']" :setting="setting" :threatenDict="threatenDict" :currentEventType="4" :severityOptions="severityOptions" @handle="eventHandle"></selected-event-list>
              <el-button v-if="setting && setting.opType === '-1'" type="text" style="width: 100%;margin-top: 10px;" icon="el-icon-plus" @click="selectEventClick">选择事件</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
    <!--  事件选择弹窗-开始  -->
    <select-event v-if="openEventSelectDialog" :selected-event-ids="formItem.eventIds" :dest-ips="formItem.associatedIps" :open.sync="openEventSelectDialog" :setting="setting" :threaten-dict="threatenDict" :current-event-type="parseInt(eventActiveName)" :severity-options="severityOptions" @cancel="openEventSelectDialog=false" @selected="handleEventSelected"/>
    <!--  事件选择弹窗-结束  -->
    <!--  业务系统-资产选择  -->
    <el-dialog v-if="applicationDialog" title=选择资产 :visible.sync="applicationDialog" class="application_dialog" width="80%" append-to-body>
      <application-select v-if="applicationDialog" ref="applicationSelect" :value="currentApplicationSelect" :isMultipleSelect="true" @cancel="applicationDialog = false" @applicationSelected="applicationSelected" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitApplicationSelect">确 定</el-button>
        <el-button @click="applicationDialog = false">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 业务系统-资产选择-结束 -->

    <!-- 事件处置弹窗-开始 -->
    <el-dialog
      title="事件处置"
      :visible.sync="showHandleDialog"
      width="600px"
      append-to-body
    >
      <el-form ref="handleForm" :model="handleForm" :rules="handleRules" label-width="106px">
        <el-form-item label="处置状态" prop="handleState">
          <el-select
            v-model="handleForm.handleState"
            placeholder="请选择处置状态"
          >
            <el-option
              v-for="dict in handleStateOption"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="处置材料" prop="handleFile">
          <file-upload v-model="handleForm.handleFile" :limit="9" :file-type="['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']" :file-size="50"></file-upload>
        </el-form-item>
        <el-form-item label="处置说明" prop="handleDesc">
          <el-input type="textarea" :rows="2" v-model="handleForm.handleDesc" maxlength="500" show-word-limit
                    placeholder="请输入处置说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitHandleForm">确 定</el-button>
        <el-button @click="showHandleDialog = false">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 事件处置弹窗-结束 -->
  </div>
</template>

<script>
import SelectedEventList from "@/views/todoItem/todo/selected_event_list.vue";
import DeptSelect from "@/views/components/select/deptSelect.vue";
import {getAllUserListByDept} from "@/api/system/user";
import SelectEvent from "@/views/todoItem/todo/select_event.vue";
import ApplicationSelect from "@/views/todoItem/todo/applicationSelect.vue";

export default {
  name: "targetCard",
  components: {ApplicationSelect, SelectEvent, DeptSelect, SelectedEventList},
  dicts: ["work_order_handle_state"],
  props: {
    formItem: {
      type: Object,
      default: () => {
        return {
          handleDept: '',
          handleUser: '',
          applicationId: '',
          loginUrl: '',
          manager: '',
          phone: '',
          eventData: {
            'type1': [],
            'type2': [],
            'type3': [],
            'type4': []
          },
        }
      }
    },
    deptData: {
      type: Object,
      default: () => {}
    },
    curIndex: {
      type: Number,
      default: null
    },
    setting: {
      type: Object,
      default: () => {}
    },
    threatenDict: {
      type: Array,
      default: () => []
    },
    activeName: {
      type: [String, Number],
      default: null
    },
  },
  data() {
    return {
      eventActiveName: '1',
      isFromEvent: false,
      currentEventType: null,
      severityOptions: {
        '1': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},
        ],
        '2': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},
        ],
        '3': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '无威胁', value: 1},{type: 'primary', label: '低危', value: 2},{type: 'warning', label: '中危', value: 3},{type: 'warning', label: '高危', value: 4},{type: 'danger', label: '严重', value: 5},
        ],
      },
      handleOption: [],
      managerOption: [],
      handleOptionCopy: [],
      managerOptionCopy: [],
      rules: {
        handleDept: [
          {required: true, message: '请选择处置部门', trigger: 'blur'}
        ],
        handleUser: [
          {required: true, message: '请选择处置人', trigger: 'blur'}
        ]
      },
      openEventSelectDialog: false,
      applicationDialog: false,
      currentApplicationSelect: null,
      showHandleDialog: false,
      handleForm: {},
      handleRules: {
        handleState: [
          {required: true, message: '请选择处置结论', trigger: 'blur'}
        ],
      },
      curHandleEvent: null,
      tempHandleUser: null,
    }
  },
  watch: {
    'formItem.handleDept': {
      handler(newVal,oldVal) {
        if(!newVal || newVal !== oldVal){
          this.$set(this.formItem, 'handleUser',null);
          this.$forceUpdate();
        }
      },
      deep: true
    },
    formItem: {
      immediate: true,
      handler(newVal,oldVal) {
        if(newVal && !newVal.eventData){
          newVal.eventData = {
            'type1': [],
            'type2': [],
            'type3': [],
            'type4': []
          }
        }
      }
    }
  },
  computed: {
    handleStateOption() {
      return this.dict.type.work_order_handle_state;
    }
  },
  mounted() {
    this.getManagerUserList();
  },
  methods: {
    removeTarget(index){
      this.$emit('remove', index);
    },
    handleUserFilter(val){
      if(val){
        this.handleOption = this.handleOptionCopy.filter(option => {
          return option.userName.indexOf(val) !== -1 || option.nickName.indexOf(val) !== -1;
        });
      }else {
        this.handleOption = [...this.handleOptionCopy];
      }
    },
    managerUserFilter(val){
      if(val){
        this.managerOption = this.managerOptionCopy.filter(option => {
          return option.userName.indexOf(val) !== -1 || option.nickName.indexOf(val) !== -1;
        });
      }else {
        this.managerOption = [...this.managerOptionCopy];
      }
    },
    getUserList(deptId) {
      getAllUserListByDept({
        deptId: deptId
      }).then(res => {
        this.handleOption = res.rows;
        this.handleOptionCopy = [...this.handleOption];
        if(this.tempHandleUser){
          let matchUser = this.handleOptionCopy.find(item => item.userId === this.tempHandleUser);
          if(!matchUser){
            this.tempHandleUser = null;
          }else {
            this.$nextTick(() => {
              this.$set(this.formItem, 'handleUser', this.tempHandleUser);
              this.tempHandleUser = null;
            })
          }
        }
      })
    },
    getManagerUserList() {
      getAllUserListByDept({queryAllData: true}).then(res => {
        this.managerOption = res.rows;
        this.managerOptionCopy = [...this.managerOption];
        if(this.formItem.manager){
          let matchUser = this.managerOptionCopy.find(item => item.userId === this.formItem.manager);
          if(!matchUser){
            this.formItem.manager = null;
          }
        }
      })
    },
    handleUserVisibleChange(){
      //this.handleOption = [...this.handleOptionCopy];
    },
    handleDeptInput(val){
      this.handleOption = [];
      if(val){
        this.getUserList(val);
      }
    },
    userSelected(val){
      this.$forceUpdate();
    },
    validate(){
      return new Promise((resolve, reject) => {
        this.$refs.form.validate(res => {
          if (res) {
            if(this.jnpf.curNodeIsMatch(this.setting,'2')){
              let eventArr = [];
              for (let i = 0; i < 4; i++) {
                if(this.formItem.eventData['type'+(i+1)] && this.formItem.eventData['type'+(i+1)].length > 0){
                  eventArr.push(...this.formItem.eventData['type'+(i+1)]);
                }
              }
              for (let i = 0; i < eventArr.length; i++) {
                if(!eventArr[i].handleState || eventArr[i].handleState === '0'){
                  reject();
                  return;
                }
              }
            }
            resolve()
          } else {
            this.$emit('updateActiveName', this.activeName.toString());
            reject()
          }
        })
      })
    },
    selectEventClick(){
      this.formItem.eventIds = this.formItem.eventData['type'+this.eventActiveName].map(item => item.eventId);
      this.openEventSelectDialog = true;
    },
    eventTabClick(val){
      this.currentEventType = val;
      this.formItem.eventIds = [];
    },
    handleEventSelected(val, event){
      let arr = [];
      if(this.formItem.eventIds && this.formItem.eventIds.length > 0){
        arr = this.formItem.eventIds;
      }
      val.forEach(item => {
        if(arr.indexOf(item) === -1){
          arr.push(item);
        }
      })
      this.formItem.eventData['type'+this.eventActiveName] = arr.map(item => {
        return {
          eventId: item
        }
      });
    },
    applicationSelected(data){
      this.$emit('applicationSelected', {
        curIndex: this.curIndex,
        activeName: this.activeName,
        data: data
      });
      this.applicationDialog = false;
    },
    handleApplicationSelected(data){
      this.formItem.applicationId = data.assetId;
      this.formItem.assetName = data.assetName;
      this.formItem.phone = data.managerPhone;
      this.formItem.manager = parseInt(data.manager);
      if(this.formItem.manager && this.managerOptionCopy && this.managerOptionCopy.length > 0){
        let matchUser = this.managerOptionCopy.find(item => item.userId === this.formItem.manager);
        if(!matchUser){
          this.formItem.manager = null;
        }
      }
      this.formItem.loginUrl = data.url;
      if(!this.formItem.handleDept){
        this.$set(this.formItem, 'handleDept', parseInt(data.deptId));
        this.handleDeptInput(this.formItem.handleDept);
      }
      if(!this.formItem.handleUser){
        this.$nextTick(() => {
          // this.$set(this.formItem, 'handleUser', this.formItem.manager);
          this.tempHandleUser = this.formItem.manager;
        })
      }
    },
    submitApplicationSelect(){
      this.$refs.applicationSelect.submit();
    },
    submitHandleForm(){
      this.$refs.handleForm.validate(res => {
        if(res){
          if(this.curHandleEvent){
            let type = this.curHandleEvent.type;
            let curEventList = this.getCurEventList(type);
            let match = curEventList.find(item => item.eventId === this.curHandleEvent.id);
            if(match){
              match.handleState = this.handleForm.handleState;
              match.curHandleState = this.handleForm.handleState;
              match.handleDesc = this.handleForm.handleDesc;
              match.handleFile = this.handleForm.handleFile;
              this.curHandleEvent.row.curHandleState = this.handleForm.handleState;
              let curRef = this.$refs['eventList'+this.curHandleEvent.type];
              if(curRef){
                curRef.doLayout();
              }
            }
          }
          this.showHandleDialog = false;
        }
      })
    },
    eventHandle(data){
      this.handleForm = {};
      let curEventList = this.getCurEventList(data.type);
      let match = curEventList.find(item => item.eventId === data.id);
      if(match){
        this.handleForm = {
          handleState: match.handleState,
          handleDesc: match.handleDesc,
          handleFile: match.handleFile
        }
      }
      this.curHandleEvent = data;
      this.showHandleDialog = true;
    },
    getCurEventList(type){
      let curEventList = [];
      if(type === 1){
        curEventList = this.formItem.eventData.type1;
      }else if(type === 2){
        curEventList = this.formItem.eventData.type2;
      }else if(type === 3){
        curEventList = this.formItem.eventData.type3;
      }else if(type === 4){
        curEventList = this.formItem.eventData.type4;
      }
      return curEventList;
    },
    getEventSize(arr){
      return arr ? arr.length : 0;
    },
  }
}

</script>

<style scoped lang="scss">
.form{
  display: flex;
  .form-content{
    width: 75%;
  }
  .form-remove{
    flex: 1;
    padding-top: 10px;
    text-align: right;
  }
}
.box-card:not(:first-child){
  margin-top: 10px;
}
::v-deep .event-list-tabs{
  .el-tabs__header{
    justify-content: start;
  }
}
</style>
