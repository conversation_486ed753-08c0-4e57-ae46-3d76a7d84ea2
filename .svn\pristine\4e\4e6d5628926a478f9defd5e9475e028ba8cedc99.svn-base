<!-- 通知公告详情 -->
<template>
  <el-dialog @open="openDialog" :visible.sync="visible" :before-close="handleClose" title="查看公告"
             width="50%">
    <div class="customForm-container">
      <el-descriptions
        class="custom-column"
        direction="vertical"
        size="medium"
        :colon="false"
        label-class-name="custom-label-style"
        content-class-name="custom-content-style"
        :column="2">
        <el-descriptions-item label="公告标题">{{ form.noticeTitle || '-' }}</el-descriptions-item>
        <el-descriptions-item label="是否置顶" v-if="!isLink">{{ form.isTop === 1 ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="公告内容" :span="3">{{ form.noticeContent || '-' }}</el-descriptions-item>
        <el-descriptions-item label="接受对象" v-if="!isLink">{{
            form.acceptObjectNames && form.acceptObjectNames.length > 0 ? form.acceptObjectNames.join('、') : '所有人'
          }}
        </el-descriptions-item>
        <el-descriptions-item label="截止时间" v-if="!isLink">{{ form.deadline || '-' }}</el-descriptions-item>
        <el-descriptions-item v-if="form.entrances && form.entrances.length > 0" label="相关入口" :span="3" class="custom-div" :content-style="{ borderBottom: 'none' }">
          <div class="custom-div-box">
            <div v-for="(item, index) in form.entrances" :key="index" class="custom-div-item">
              <div><span>类型：</span>{{
                  item.entranceType === 'link' ? '链接' : item.entranceType === 'affair' ? '事务' : '快捷入口'
                }}
              </div>
              <div style="display: flex; white-space: nowrap;" v-if="isLink">
                <span v-if="item.entranceType === 'link'">地址：</span>
                <span v-else>入口：</span>
                <a v-if="item.entranceType === 'link'" :href="item.entranceUrl" target="_blank" class="link-name-skip" :title="item.entranceUrl">{{ item.entranceUrl || '-' }}</a>
                <span v-if="item.entranceType === 'affair'" @click="handleEntranceClick(item)" class="link-name-skip" :title="item.resolvedName">{{ item.resolvedName || '-' }}</span>
                <a  v-else :href="item.entranceUrl" class="link-name-skip">{{ item.resolvedName || '-' }}</a>
              </div>
              <div style="display: flex; white-space: nowrap;" v-else>
                <span v-if="item.entranceType === 'link'">地址：</span>
                <span v-else>入口：</span>
                <span v-if="item.entranceType === 'link'" class="link-name" :title="item.entranceUrl">{{ item.entranceUrl || '-' }}</span>
                <span  v-else class="link-name" :title="item.resolvedName">{{ item.resolvedName || '-' }}</span>
              </div>
              <div style="display: flex"><span>显示名称：</span>{{ item.entranceName || '-' }}</div>
            </div>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="是否弹窗" v-if="!isLink">{{ form.isPopup === '1' ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="弹窗文案" v-if="form.isPopup === '1' && !isLink">{{ form.popupContent || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>
  </el-dialog>
</template>

<script>

export default {
  name: "NoticeDetailsDialog",
  props: {
    noticeDetailVisible: {
      type: Boolean,
      default: false
    },
    noticeDetails: {
      type: Object,
      default: function () {
        return {};
      }
    },
    isLink: {
      type: Boolean,
      default: false
    }
  },
  dicts: ['sys_yes_no', 'impt_grade'], // 保留字典配置
  data() {
    return {
      form: {},
    };
  },
  watch: {},
  computed: {
    visible: {
      get() {
        return this.noticeDetailVisible;
      },
      set(val) {
        this.$emit("update:noticeDetailVisible", val);
      }
    },
    resolvedName() {
      return this.form.entrances ? this.form.entrances.map(item => item.resolvedName) : [];
    }

  },
  methods: {
    openDialog() {
      this.form = this.noticeDetails;
    },
    handleClose() {
      this.visible = false;
      this.$emit('refresh');
    },
    handleEntranceClick(item) {
      this.$emit('openAffair',  item);
    }
  }
};
</script>

<style lang="scss" scoped>
@import "@/assets/styles/customForm";

.link-name {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.link-name-skip {
  cursor: pointer;
  color: #0000ee;
  text-decoration: underline;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.custom-div-box {
  .custom-div-item {
    display: flex;

    div {
      width: calc(100% / 3);
      border-bottom: 1px solid #ebeef5;
      margin-right: 40px;
      color: #333;
      padding: 0 0 5px 0;
    }

    /* 第二个div的特殊样式 */
    &:nth-child(2) {
      margin: 20px 0;
    }
  }
}
</style>
