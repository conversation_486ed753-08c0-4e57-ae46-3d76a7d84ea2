<template>
  <div class="report-target-main">
    <div class="head" style="display: flex;">
      <el-tabs ref="deptDataTabs" v-model="activeName" v-if="deptDataList && deptDataList.length>0" :key="deptDataList.length" @tab-click="tabClick">
        <el-tab-pane :label="item.deptName + ' (' + item.formData.length + ')'" :key="index" :name="index.toString()" v-for="(item,index) in deptDataList" v-if="item.formData && item.formData.length>0">
          <div class="content" v-if="item.formData && item.formData.length>0">
            <target-card class="target-card" :ref="'targetForm'+itemIndex" :active-name.sync="index" :form-item="formItem" :setting="setting"
                         :cur-index="itemIndex" :key="index + '' + itemIndex" v-for="(formItem,itemIndex) in item.formData" :deptData="item"
                         @updateActiveName="updateActiveName" @remove="removeTarget" @applicationSelected="applicationSelected" />
            <div class="add-event-btn" @click="addTarget" v-if="setting && setting.opType === '-1'">+新增对象</div>
          </div>
        </el-tab-pane>
      </el-tabs>
<!--      <div class="head-right"><div @click="addDept" style="cursor: pointer;">+新增单位</div></div>-->
    </div>

    <el-dialog title="新增通报对象" :visible.sync="dialogAddDeptVisible" :append-to-body="true">
      <el-form ref="addDeptForm" :model="addDeptForm" :rules="addDeptRules" label-width="80px">
        <el-form-item label="责任部门" prop="deptId">
          <treeselect v-model="addDeptForm.deptId" :options="deptOptions" :normalizer="normalizer" placeholder="选择部门" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitDeptForm">确 定</el-button>
        <el-button @click="dialogAddDeptVisible = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SelectedEventList from "@/views/todoItem/todo/selected_event_list.vue";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import {addDept, listDept, updateDept} from "@/api/system/dept";
import DeptSelect from "@/views/components/select/deptSelect.vue";
import TargetCard from "@/views/todoItem/todo/target_card.vue";
import {getAllUserListByDept} from "@/api/system/user";
export default {
  name: 'report-target',
  components: {
    DeptSelect,
    SelectedEventList,
    Treeselect,
    TargetCard
  },
  props: {
    setting: {
      type: Object,
      default: () => {}
    },
    threatenDict: {
      type: Array,
      default: () => []
    },
    deptDataList: {
      type: Array,
      default: () => []
    },
  },
  data () {
    return {
      activeName: null,
      activeNameList: [],
      form: {},
      isFromEvent: false,
      currentEventType: null,
      severityOptions: {
        '1': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},
        ],
        '2': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},
        ],
        '3': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '无威胁', value: 1},{type: 'primary', label: '低危', value: 2},{type: 'warning', label: '中危', value: 3},{type: 'warning', label: '高危', value: 4},{type: 'danger', label: '严重', value: 5},
        ],
      },
      eventActiveName: null,
      dialogAddDeptVisible: false,
      addDeptForm: {},
      addDeptRules: {
        deptId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ]
      },
      deptOptions: [],
      handleOption: {},
      manageOption: [],
      handleOptionCopy: [],
      manageOptionCopy: [],
    }
  },
  mounted() {
  },
  watch: {
    deptDataList: {
      handler(val){
        if(this.jnpf.curNodeIsMatch(this.setting,'2')){
          //处置节点 排除非自己的通报对象
          let myUserId = sessionStorage.getItem('userId');
          for (let i = 0; i < val.length; i++) {
            let item = val[i];
            for (let i = item.formData.length - 1; i >= 0; i--) {
              if (item.formData[i].handleUser.toString() !== myUserId) {
                item.formData.splice(i, 1); // 删除当前索引的元素
              }
            }
          }
          this.$nextTick(()=>{
            let deptDataTabs = this.$refs.deptDataTabs;
            if(deptDataTabs && deptDataTabs.panes && deptDataTabs.panes.length > 0){
              this.activeName = deptDataTabs.panes[0].name;
            }
          })
        }else {
          //地市审核
          //先找到当前用户节点
          if(this.setting && this.setting.opType === 1 && this.setting.flowTaskNodeList && this.setting.flowTaskNodeList.length > 0){
            let curUserNodeList = this.setting.flowTaskNodeList.filter(taskNode => taskNode.isCurUserNode);
            if(curUserNodeList && curUserNodeList.length > 0){
              let handleUserArr = [];
              curUserNodeList.forEach(curUserNode => {
                //找到这个节点的上级
                let nodeUp = this.setting.flowTaskNodeList.find(taskNode => taskNode.nodeCode === curUserNode.nodeUp);
                if(nodeUp){
                  //判断这个节点是否为处置节点
                  let nodeUpProp = JSON.parse(nodeUp.nodePropertyJson);
                  if(this.jnpf.nodeVarStateIsMatch(nodeUpProp.properties.flowVariable,'2')){
                    //查一下处置节点的人
                    let handleUser = null;
                    this.setting.flowTaskOperatorRecordList.forEach(record => {
                      if(record.nodeCode === nodeUp.nodeCode){
                        handleUser = record.handleId;
                      }
                    })
                    if(handleUser){
                      handleUserArr.push(handleUser);
                    }
                  }
                }
              })
              //获取匹配的记录
              if(handleUserArr && handleUserArr.length > 0){
                for (let i = 0; i < val.length; i++) {
                  let item = val[i];
                  for (let i = item.formData.length - 1; i >= 0; i--) {
                    let matchUser = handleUserArr.find(handleUserId => handleUserId === item.formData[i].handleUser.toString())
                    if (!matchUser) {
                      item.formData.splice(i, 1); // 删除当前索引的元素
                    }
                  }
                }
                this.$nextTick(()=>{
                  let deptDataTabs = this.$refs.deptDataTabs;
                  if(deptDataTabs && deptDataTabs.panes && deptDataTabs.panes.length > 0){
                    this.activeName = deptDataTabs.panes[0].name;
                  }
                })
              }
            }
          }
        }
      }
    }
  },
  methods: {
    addTarget(){
      if(!this.deptDataList[parseInt(this.activeName)].formData || this.deptDataList[parseInt(this.activeName)].formData.length < 1){
        this.deptDataList[parseInt(this.activeName)].formData = [];
      }
      this.deptDataList[parseInt(this.activeName)].formData.push({
        eventData: {
          'type1': [],
          'type2': [],
          'type3': [],
          'type4': []
        }
      })
      this.$forceUpdate();
    },
    removeTarget(index){
      this.deptDataList[parseInt(this.activeName)].formData.splice(index,1);
      if(!this.deptDataList[parseInt(this.activeName)].formData || this.deptDataList[parseInt(this.activeName)].formData.length < 1){
        //移除
        this.deptDataList.splice(parseInt(this.activeName),1);
        this.activeName = null;
      }
      this.$forceUpdate();
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.deptId,
        label: node.deptName,
        children: node.children
      };
    },
    submitDeptForm(){
      this.$refs["addDeptForm"].validate(valid => {
        if (valid) {
          let selectedMatch = this.deptDataList.find(item => item.deptId === this.addDeptForm.deptId);
          if(!selectedMatch){
            //不存在 加入
            let matchDept = this.loopGetDept(this.deptOptions,this.addDeptForm.deptId);
            this.deptDataList.push({
              deptId: this.addDeptForm.deptId,
              deptName: matchDept.deptName,
              formData: [{
                eventData: {
                  'type1': [],
                  'type2': [],
                  'type3': [],
                  'type4': []
                }
              }]
            });
            this.$nextTick(()=>{
              this.activeName = (this.deptDataList.length - 1).toString();
            })
          }
          this.dialogAddDeptVisible = false;
        }
      });
    },
    loopGetDept(deptOptions,value){
      // 创建一个栈用于存储待检查的节点
      const stack = [...deptOptions];

      // 深度优先遍历（非递归实现）
      while (stack.length) {
        const node = stack.pop();
        // 找到匹配的节点
        if (node.deptId === value) {
          return node;
        }
        // 将子节点压入栈中
        if (node.children && node.children.length) {
          stack.push(...node.children.reverse()); // 逆序入栈保证原始顺序
        }
      }

      return null; // 未找到
    },
    addDept(){
      listDept().then(response => {
        this.deptOptions = this.handleTree(response.data, "deptId");
        this.dialogAddDeptVisible = true;
      });
    },
    handleUserFilter(val){
      console.log(val)
      if(val){
        this.handleOption = this.handleOptionCopy.filter(option => {
          return option.userName.indexOf(val) !== -1 || option.nickName.indexOf(val) !== -1;
        });
      }else {
        this.handleOption = [...this.handleOptionCopy];
      }
    },
    getUserList(deptId) {
      getAllUserListByDept({
        deptId: deptId
      }).then(res => {
        this.handleOption[deptId.toString()] = res.rows;
        this.$forceUpdate();
      })
    },
    handleUserVisibleChange(){
      //this.handleOption = [...this.handleOptionCopy];
    },
    handleDeptInput(val){
      console.log(val)
      this.handleOption[val.toString()] = [];
      console.log(this.deptDataList)
      if(val){
        this.getUserList(val);
      }
    },
    validate() {
      this.activeNameList = [];
      let result = [];
      if(this.deptDataList && this.deptDataList.length > 0){
        this.deptDataList.forEach((item,index) => {
          if(item.formData && item.formData.length > 0){
            for (let i = 0; i < item.formData.length; i++) {
              let refs = 'targetForm'+i;
              this.$refs[refs].forEach(ref => {
                result.push(ref.validate());
              });
            }
          }
        });
      }else {
        this.$message.error('请选择通报对象');
        result.push(new Promise((resolve, reject) => {
          reject(new Error('请选择通报对象'));
        }));
        return result;
      }
      if(this.activeNameList && this.activeNameList.length > 0){
        //排序
        let arr = this.activeNameList.sort((a,b)=>{
          return a - b;
        })
        this.activeName = arr[0].toString();
      }
      //是否有未处置的
      if(this.jnpf.curNodeIsMatch(this.setting,'2')){
        let noHandleFlag = false;
        this.deptDataList.forEach(item => {
          if(item.formData && item.formData.length > 0){
            item.formData.forEach(formDataItem => {
              if(formDataItem.eventData){
                Object.values(formDataItem.eventData).forEach(eventDataItem => {
                  if(eventDataItem && eventDataItem.length > 0){
                    eventDataItem.forEach(eventItem => {
                      if(!eventItem.handleState || eventItem.handleState === '0'){
                        noHandleFlag = true;
                      }
                    })
                  }
                })
              }
            })
          }
        });
        if(noHandleFlag){
          this.$message.error('请先处置未处置的事件');
        }
      }
      return result;
    },
    updateActiveName(val){
      this.activeNameList.push(parseInt(val));
    },
    submitForm(){
      return this.deptDataList;
    },
    applicationSelected(data) {
      let curIndex = data.curIndex;
      const curActiveName = data.activeName;
      let applicationDataArr = data.data;

      // 如果没有数据直接返回
      if (!applicationDataArr || applicationDataArr.length === 0) return;

      let curFormData = this.deptDataList[curActiveName].formData;
      let allocatedIndexes = []; // 记录已分配索引位置

      // 1. 优先将第一个元素分配给当前选中的索引项
      if (curIndex !== null && applicationDataArr.length > 0) {
        // 确保存在当前索引项
        if (!curFormData[curIndex]) {
          // 当前索引不存在则添加
          curFormData.push({
            eventData: {
              type1: [], type2: [], type3: [], type4: []
            }
          });
        }

        // 分配第一个业务系统给当前选中项
        this.$nextTick(() => {
          let refs = this.$refs[`targetForm${curIndex}`];
          if(refs){
            let matchRef = refs.find(ref => ref.activeName === curActiveName);
            matchRef && matchRef.handleApplicationSelected(applicationDataArr[0]);
          }
        });
        allocatedIndexes.push(curIndex);
      }

      // 2. 处理剩余的业务系统数据
      let remainingItems = allocatedIndexes.length > 0
        ? applicationDataArr.slice(1)
        : [...applicationDataArr];

      if (remainingItems.length > 0) {
        // 查找未分配业务系统的空位置
        let emptySlots = [];
        curFormData.forEach((item, index) => {
          if (!item.applicationId && !allocatedIndexes.includes(index)) {
            emptySlots.push(index);
          }
        });

        // 优先分配到已有空位置
        while (remainingItems.length > 0 && emptySlots.length > 0) {
          let slotIndex = emptySlots.shift();
          let itemToAssign = remainingItems.shift();
          this.$nextTick(() => {
            this.$refs[`targetForm${slotIndex}`][curActiveName]?.handleApplicationSelected(itemToAssign);
          });
          allocatedIndexes.push(slotIndex);
        }

        // 如果还有剩余项目且空位不足，添加新表单项目
        while (remainingItems.length > 0) {
          let itemToAssign = remainingItems.shift();
          let newIndex = curFormData.length;

          // 添加新表单项
          curFormData.push({
            eventData: {
              type1: [], type2: [], type3: [], type4: []
            }
          });

          // 将业务系统分配给新表单项
          this.$nextTick(() => {
            // 稍作延迟确保DOM更新完成
            setTimeout(() => {
              this.$refs[`targetForm${newIndex}`]?.[curActiveName]?.handleApplicationSelected(itemToAssign);
            }, 300);
          });

          allocatedIndexes.push(newIndex);
        }
      }
    },
    tabClick(tabs){
      console.log(tabs)
    },
    /*applicationSelected(data){
      let curIndex = data.curIndex;
      let curActiveName = data.activeName;
      console.log(this.deptDataList[curActiveName].formData)
      let applicationDataArr = data.data;
      if(applicationDataArr && applicationDataArr.length > 0){
        let curFormData = this.deptDataList[curActiveName].formData;
        //排除已经选择了业务系统的数据以及业务系统存在的数据
        let existNoAppIndexArr = [];
        for (let i = 0; i < applicationDataArr.length; i++) {
          const curItem = applicationDataArr[i];
          let matchItem = curFormData.find(item => curItem.assetId === item.applicationId);
          if(!matchItem){
            //不存在 增加
            let noAppItemIndex = null;
            for (let j = 0; j < curFormData.length; j++) {
              if(!curFormData[j].applicationId && existNoAppIndexArr.indexOf(j) === -1){
                noAppItemIndex = j;
                existNoAppIndexArr.push(j);
                break;
              }
            }
            if(noAppItemIndex != null){
              //有没选择的对象 直接给它
              this.$nextTick(() => {
                this.$refs['targetForm'+noAppItemIndex][curActiveName].handleApplicationSelected(curItem);
              })
            }else {
              //没有 新增对象
              this.deptDataList[curActiveName].formData.push({
                eventData: {
                  'type1': [],
                  'type2': [],
                  'type3': [],
                  'type4': []
                }
              });
              existNoAppIndexArr.push(this.deptDataList[curActiveName].formData.length - 1);
              // this.$refs['targetForm'+(this.deptDataList[curActiveName].formData.length - 1)][curActiveName].handleApplicationSelected(curItem);
              const index = this.deptDataList[curActiveName].formData.length - 1;
              this.$nextTick(() => {
                setTimeout(() => {
                  this.$refs['targetForm'+index][curActiveName].handleApplicationSelected(curItem);
                },300);
              })
            }
          }
        }
      }
    },*/
  },
}

</script>

<style scoped lang="scss">
.report-target-main{
  .head{
    padding-bottom: 1px;
    position: relative;
    ::v-deep .el-tabs{
      width: 100%;
    }
    ::v-deep .el-tabs__header{
      justify-content: left !important;
    }
    .head-right{
      position: absolute;
      right: 10px;
      top: 10px;
      align-content: center;
      text-align: right;
      font-size: 14px;
      color: #6c6c6c;
      justify-content: right;
      flex: 1;
      display: flex;
    }
  }
  .content{
    margin-top: 10px;
    .add-event-btn{
      text-align: center;
      font-size: 14px;
      color: #6c6c6c;
      cursor: pointer;
      margin-top: 10px;
    }
  }

  .target-card:not(:first-child){
    margin-top: 10px;
  }
}
</style>
