<template>
  <div>
    <el-table :data="eventList" ref="eventList" row-key="id" v-if="currentEventType !== 4" :key="curKey">
      <el-table-column label="漏洞名称" min-width="120" prop="title"  show-overflow-tooltip/>
      <el-table-column label="漏洞类型" prop="category"  width="100">
        <template slot-scope="scope">
          <dict-tag :options="categoryDict" :value="scope.row.category"/>
        </template>
      </el-table-column>
      <el-table-column label="漏洞等级" prop="severity" width="100">
        <template slot-scope="scope">
          <el-tag :type="handleSeverityTag(scope.row.severity,'type')">{{handleSeverityTag(scope.row.severity,'label')}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="影响目标" min-width="120" prop="webUrl"  show-overflow-tooltip/>
      <el-table-column label="协议" width="100" prop="protocol"  show-overflow-tooltip/>
      <el-table-column label="端口" width="80" prop="hostPort"  show-overflow-tooltip/>

      <el-table-column label="数据来源"  prop="dataSource" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.dataSource == '1'">探测</span>
          <span v-else-if="scope.row.dataSource == '2'">手动</span>
          <span v-else-if="scope.row.dataSource == '8'">流量</span>
          <span v-else-if="scope.row.dataSource == '9'">流量</span>
        </template>
      </el-table-column>
      <el-table-column label="发现次数"  prop="scanNum" width="100"/>
      <el-table-column label="发现时间" prop="createTime"  width="180">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
        </template>
      </el-table-column>
      <el-table-column label="处置状态" prop="curHandleState" width="120" :formatter="handleStateFormatter"/>
      <el-table-column label="操作" fixed="right" width="150" :show-overflow-tooltip="false" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            @click="handleEventDetail(scope.row)"
          >查看
          </el-button>
          <el-button
            size="mini"
            type="text"
            @click="toHandle(scope.row)"
            v-if="jnpf.curNodeIsMatch(setting,'2')"
          >处置
          </el-button>
<!--          <el-button
            size="mini"
            type="text"
            @click="toCheck(scope.row)"
            v-if="jnpf.curNodeIsMatch(setting,'3')"
          >核验
          </el-button>-->
          <el-button
            v-if="!setting.readonly && setting && setting.opType === '-1'"
            style="color: #f56c6c;"
            size="mini"
            type="text"
            @click="handleEventRemove(scope.row)"
          >移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-table :data="eventList" ref="eventList" row-key="id" v-if="currentEventType === 4">
      <el-table-column
        label="影响资产主IP"
        prop="hostIp"
        min-width="200"
      />
      <el-table-column
        label="协议"
        prop="serviceType"
        width="80"
      />
      <el-table-column
        label="端口"
        prop="hostPort"
        width="120"
      />
      <el-table-column
        label="用户名"
        prop="username"
        min-width="120"
      >
      </el-table-column>
      <el-table-column
        label="密码"
        prop="weakPassword"
        min-width="120"
      >
      </el-table-column>

      <el-table-column
        label="所属部门"
        prop="deptName"
        min-width="120"
      />

      <el-table-column
        label="最近漏洞时间"
        prop="updateTime"
        width="160"
      >
        <template slot-scope="scope">
          {{ parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
        </template>
      </el-table-column>
      <el-table-column label="处置状态" prop="curHandleState" width="120"  :formatter="handleStateFormatter"/>
      <el-table-column label="操作" fixed="right" width="120" :show-overflow-tooltip="false" class-name="small-padding fixed-width">
        <template slot-scope="scope">
<!--          <el-button
            size="mini"
            type="text"
            v-if="currentEventType !== 4"
            @click="handleEventDetail(scope.row)"
          >查看
          </el-button>-->
          <el-button
            size="mini"
            type="text"
            @click="toHandle(scope.row)"
            v-if="jnpf.curNodeIsMatch(setting,'2')"
          >处置
          </el-button>
          <el-button
            v-if="!setting.readonly && setting && setting.opType === '-1'"
            style="color: #f56c6c;"
            size="mini"
            type="text"
            @click="handleEventRemove(scope.row)"
          >移除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryEventParams.pageNum"
      :limit.sync="queryEventParams.pageSize"
      @pagination="getEventDataList"
    />

    <el-dialog v-if="openEventDetailDialog" title="查看事件详情" :visible.sync="openEventDetailDialog" width="80%" append-to-body>
      <alarm-detail v-if="openEventDetailDialog && currentEventType == 3" @openDetail="openDetail" :asset-data="assetData"/>
      <new-addloophole v-if="openEventDetailDialog && currentEventType == 1" :loophole-data="assetData" :editable="false" @cancel="openEventDetailDialog=false" />
      <new-addwebvuln v-if="openEventDetailDialog && currentEventType == 2" :webvuln-data="assetData" :editable="false" @cancel="openEventDetailDialog=false" />
    </el-dialog>
  </div>
</template>

<script>
import newAddwebvuln from '@/views/frailty/webvuln/newAddwebvuln.vue'
import AlarmDetail from '@/views/basis/securityWarn/alarmDetail.vue'
import newAddloophole from '@/views/frailty/loophole/newAddloophole.vue'
import {listWebVuln} from "@/api/monitor2/webvuln"; //web漏洞
import {getVulnDealList} from "@/api/monitor2/assetFrailty"; //IP漏洞
import {listWpresult} from "@/api/monitor2/wpresult"; //弱口令
import {listAlarm} from "@/api/threaten/threatenWarn";
import * as events from "events"; //威胁事件

export default {
  name: 'SelectedEventList',
  components: { newAddloophole, AlarmDetail, newAddwebvuln },
  dicts: [
    'loophole_category',
    'threaten_alarm_type',
    "work_order_handle_state"
  ],
  props: {
    eventIds: {
      type: Array,
      default: () => []
    },
    setting: {
      type: Object,
      default: () => {}
    },
    threatenDict: {
      type: Array,
      default: () => []
    },
    currentEventType: {
      type: Number,
      default: 0
    },
    severityOptions: {
      type: Object,
      default: () => {}
    },
    isFromEvent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      assetData: [],
      openEventDetailDialog: false,
      eventList: [],
      eventListLoading: false,
      queryEventParams: {
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      curKey: 0
    }
  },
  watch: {
    eventIds: {
      deep: true,
      immediate: true,
      handler(val){
        if(val && val.length>0){
          this.getEventDataList();
        }else {
          this.eventList = [];
          this.total = 0;
        }
      }
    }
  },
  computed: {
    categoryDict(){
      if(this.currentEventType == 1 || this.currentEventType == 2){
        return this.dict.type.loophole_category;
      }else if(this.currentEventType == 3){
        return this.threatenDict;
      }
    },
    handleStateOption() {
      return this.dict.type.work_order_handle_state;
    },
  },
  methods: {
    getEventDataList(){
      if(this.currentEventType == 0 || !this.eventIds || this.eventIds.length < 1){
        return false;
      }
      this.eventListLoading = true;
      this.requestList().then(response => {
        let resData = this.convertEventData(response.rows);
        this.eventList = resData;
        this.total= response.total;
        this.eventListLoading = false;
      })
    },
    //转换
    convertEventData(srcDataList){
      if(!srcDataList){
        return [];
      }
      //匹配状态
      this.eventIds.forEach(eventId => {
        let match = srcDataList.find(item => item.id === eventId.eventId);
        if(match){
          match.curHandleState = eventId.handleState?eventId.handleState.toString():'';
        }
      })

      if(this.currentEventType == 3){
        return srcDataList.map(item => {
          return {
            id: item.id,
            title: item.threatenName,
            category: item.threatenType,
            severity: item.alarmLevel,
            webUrl: item.destIp,
            handleStatus: item.handleState,
            curHandleState: item.curHandleState,
            workState: item.orderState,
            dataSource: item.dataSource,
            scanNum: item.alarmNum,
            updateTime: item.updateTime,
            createTime: item.createTime,
            hostPort: item.destPort,
            flowState: item.flowState,
            threatenType: item.threatenType
          }
        })
      }else if(this.currentEventType == 1){
        return srcDataList.map(item => {
          item.webUrl = item.hostIp;
          return item;
        });
      }else {
        return srcDataList;
      }

    },
    requestList(){
      let queryParams = this.convertQueryParams(this.queryEventParams);
      queryParams.queryAllData = true;
      if(this.currentEventType == 1){
        //IP漏洞事件
        return getVulnDealList(queryParams);
      }else if(this.currentEventType == 2){
        //应用漏洞事件
        return listWebVuln(queryParams);
      }else if(this.currentEventType == 3){
        //威胁事件
        return listAlarm(queryParams);
      }else if(this.currentEventType == 4){
        //弱口令
        return listWpresult(queryParams);
      }
    },
    convertQueryParams(srcParams){
      srcParams.ids = this.eventIds.map(item => item.eventId);
      if(this.currentEventType == 3){
        return {
          pageNum: srcParams.pageNum,
          pageSize: srcParams.pageSize,
          threatenName: srcParams.title,
          threatenType: srcParams.category?srcParams.category.join('/'):null,
          alarmLevel: srcParams.severity,
          destIp: srcParams.webUrl,
          destPort: srcParams.hostPort,
          handleState: srcParams.handleStatus,
          orderState: srcParams.workState,
          dataSource: srcParams.dataSource,
          createTime: srcParams.createTime,
          ids: srcParams.ids
        }
      }else if(this.currentEventType == 1){
        srcParams.hostIp = srcParams.webUrl;
        return srcParams;
      }else {
        return srcParams;
      }
    },
    isRead(name){
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.read;
      }else {
        return true;
      }
    },
    isWrite(name){
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.write;
      }else {
        return true;
      }
    },
    handleSeverityTag(severity,key){
      if(!severity){
        return '未知';
      }
      if(this.severityOptions[this.currentEventType.toString()]){
        let matchItem = this.severityOptions[this.currentEventType.toString()].find(item => item.value == severity);
        if(!matchItem){
          return '未知';
        }
        return matchItem[key];
      }
      return '';
    },
    handleEventRemove(row){
      this.eventList = this.eventList.filter(item => item.id != row.id);
      this.$emit('update:eventIds',this.eventList.map(item => {
        return {
          eventId: item.id
        }
      }));
    },
    handleEventDetail(row){
      this.assetData= {...row};
      this.openDetail(true);
    },
    openDetail(val){
      this.openEventDetailDialog = val;
    },
    isFirstSelectedEvent(row){
      return false;
      if(this.setting && this.setting.rows && this.setting.rows.length > 0){
        return false;
      }
      if(!this.isFromEvent){
        return false;
      }
      if(!row){
        return true;
      }
      return this.eventIds.length > 0 && this.eventIds[0].eventId == row.id;
    },
    handleStateFormatter(row, column, cellValue, index) {
      if(cellValue === null || cellValue === undefined){
        return '未处置';
      }
      let name = '未处置';
      let match = this.handleStateOption.find(item => item.value === cellValue.toString());
      if (match) {
        name = match.label;
      }
      return name;
    },
    toHandle(row){
      this.$emit('handle',{
        id: row.id,
        type: this.currentEventType,
        row: row
      })
    },
    toCheck(){
      this.$emit('handle',{
        id: row.id,
        type: this.currentEventType
      })
    },
    doLayout(){
      this.$nextTick(() => {
        this.curKey++;
        this.$refs.eventList.doLayout();
      })
    },
  }
}
</script>

<style scoped lang="scss">

</style>
