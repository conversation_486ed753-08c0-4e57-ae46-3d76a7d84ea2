<!--通知公告详情对话框-->
<template>
  <el-dialog @open="openDialog" :title="title" :visible.sync="dialogVisible" width="50%" append-to-body>
    <el-form ref="form" :model="form" :rules="rules" :disabled="disabled">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="公告标题" prop="noticeTitle">
            <el-input v-model="form.noticeTitle" placeholder="请输入公告标题"/>
          </el-form-item>
        </el-col>
        <el-col :span="12" style="display: flex">
          <el-form-item label="是否置顶" prop="isTop">
            <el-radio-group v-model="form.isTop">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="公告内容" prop="noticeContent">
            <el-input v-model="form.noticeContent" type="textarea" placeholder="请输入内容"/>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="接受对象" prop="acceptIds">
            <receiving-object
              :multiple="true"
              v-model="form.acceptIds"
              :accept-type.sync="acceptType"
              placeholder="请选择接受对象"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="截止时间" prop="deadline">
            <el-date-picker
              style="width: 100%"
              v-model="form.deadline"
              type="datetime"
              placeholder="选择截止时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="00:00:00"
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item label="相关入口" style="margin-bottom: 5px">
            <img src="@/assets/icons/plus.png" class="plus-img" alt="plus" @click="addEntrance"/>
          </el-form-item>
          <div v-for="(item, index) in moreEntranceList" :key="index">
            <el-form ref="entranceForm" :model="item">
              <el-row :gutter="10">
                <el-col :span="6">
                  <el-form-item>
                    <el-select v-model="item.entranceType" placeholder="请选择入口" filterable clearable>
                      <el-option
                        v-for="(ele, index) in entranceArray"
                        :key="index"
                        :label="ele.label"
                        :value="ele.entranceType"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item v-if="item.entranceType === 'link'">
                    <el-input maxlength="200" clearable v-model="item.entranceUrl" placeholder="请输入链接"/>
                  </el-form-item>
                  <el-form-item v-else-if="item.entranceType === 'affair'">
                    <el-select v-model="item.entranceId" placeholder="请选择" filterable clearable>
                      <el-option
                        v-for="(ele, index) in operateWorkList"
                        :key="ele.id"
                        :label="ele.workName"
                        :value="ele.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item v-else>
                    <select-tree
                      v-model="item.entranceId"
                      :props="{
                      value: 'value',
                      label: 'label',
                      children: 'children',
                      disabled: function(data, node) {
                        return data.children && data.children.length > 0;
                      }
                    }"
                      :options="menuOptions"
                      :clearable="true"
                      :accordion="true"
                      placeholder="请选择快捷入口"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="7">
                  <el-form-item>
                    <el-input v-model="item.entranceName" clearable placeholder="请输入显示名称"/>
                  </el-form-item>
                </el-col>
                <el-col :span="1">
                  <el-form-item>
                    <div class="del-box" @click="removeEntrance(index)">
                      <i style="font-size: 24px; color: red" class="el-icon-delete"></i>
                    </div>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="display: flex">
          <el-form-item label="是否弹窗" prop="isPopup">
            <el-radio-group v-model="form.isPopup">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="form.isPopup === '1'">
          <el-form-item label="弹窗文案" prop="popupContent">
            <el-input v-model="form.popupContent" type="textarea" placeholder="请输入弹窗文案"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" v-if="!disabled" class="dialog-footer">
      <el-button type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {addNotice, getAllTreeselect, updateNotice} from "@/api/system/notice";
import ReceivingObject from "./ReceivingObject.vue"
import {getOperateWorkCreateList} from "@/api/operateWork/operateWork";
import SelectTree from "./CustomSelectTree.vue";

export default {
  name: "NoticeDialog",
  components: {
    SelectTree,
    ReceivingObject
  },
  dicts: ['sys_notice_status', 'sys_notice_type'],
  props: {
    noticeDialogVisible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: "添加公告"
    },
    noticeDetails: {
      type: Object,
      default: function () {
        return {};
      }
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      noticeList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        noticeTitle: [
          {required: true, message: "公告标题不能为空", trigger: "blur"},
          {max: 50, message: "公告标题长度不能超过50个字符", trigger: "blur"}
        ],
        noticeType: [
          {required: true, message: "公告类型不能为空", trigger: "change"}
        ],
        noticeContent: [
          {required: true, message: "公告内容不能为空", trigger: "blur"},
          {max: 500, message: "公告内容长度不能超过500个字符", trigger: "blur"}
        ],
        isTop: [
          {required: true, message: "置顶状态不能为空", trigger: "change"}
        ],
        acceptIds: [
          {required: true, message: "接收对象不能为空", trigger: "change"}
        ],
        deadline: [
          {required: true, message: "截止时间不能为空", trigger: "blur"}
        ],
        isPopup: [
          {required: true, message: "是否弹窗不能为空", trigger: "change"}
        ],
        popupContent: [
          {required: true, message: "弹窗文案不能为空", trigger: "blur"},
          {max: 500, message: "弹窗文案长度不能超过500个字符", trigger: "blur"}
        ]
      },
      queryParams: {},
      operateWorkList: [], // 事务列表
      menuOptions: [], // 菜单列表(快捷入口)
      acceptType: '',
      moreEntranceList: [
        {entranceType: 'link'},    // 默认显示链接输入框
        {entranceType: 'affair'},  // 默认显示事务选择框
        {entranceType: 'shortcut'} // 默认显示快捷入口树
      ],
      entranceArray: [
        {
          label: "链接",
          entranceType: 'link',
          entrance_url: "",
          enable: true
        },
        {
          label: "事务",
          entranceType: 'affair',
          entrance_url: "",
          enable: true
        },
        {
          label: "快捷入口",
          entranceType: 'shortcut',
          entrance_url: "",
          enable: true
        },
      ],
    }
  },

  computed: {
    dialogVisible: {
      get() {
        return this.noticeDialogVisible;
      },
      set(val) {
        this.$emit('update:noticeDialogVisible', val);
      }
    },
  },
  methods: {
    openDialog() {
      this.reset();
      this.form = this.noticeDetails;
      if (this.form.status === "2") this.form.status = "0";
      this.getMenuTreeselect();
      this.getOperateWorkCreateListData();
      if (this.form.noticeId !== undefined) {
        this.acceptType = this.form.acceptType;
        this.moreEntranceList = this.form.entrances;
        this.moreEntranceList.forEach(item => {
          if (item.entranceType === "affair") {
            item.entranceId = Number(item.entranceId);
          }
        });
        this.form.acceptIds = JSON.parse(this.form.acceptIds);
      }
    },

    addEntrance() {
      this.moreEntranceList.push({});
    },

    removeEntrance(index) {
      this.moreEntranceList.splice(index, 1);
    },

    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          let params = {...this.form};
          params.acceptType = this.acceptType;
          params.noticeType = '1'; // 默认为通知公告
          if (this.form.acceptType === 'all') {
            params.acceptIds = [];
          }else {
            params.acceptIds = JSON.stringify(this.form.acceptIds);
          }
          params.entrances = this.moreEntranceList;
          addNotice(params).then(() => {
            if (this.form.noticeId !== undefined) {
              this.$modal.msgSuccess("复制成功");
            }else {
              this.$modal.msgSuccess("新增成功");
            }
            this.dialogVisible = false;
            this.$emit('refresh');
          });
        }
      });
    },
    cancel() {
      this.reset();
      this.dialogVisible = false;
    },

    // 获取事务列表
    getOperateWorkCreateListData() {
      getOperateWorkCreateList(this.queryParams).then(res => {
        this.operateWorkList = res.data.flatMap(item => item.operateWorkList);
      })
    },

    /** 查询菜单树结构 */
    getMenuTreeselect() {
      getAllTreeselect().then(response => {
        let menuOptions = response.data.map((item, index) => {
          if (index === 0 && item.children) {
            const {children, ...rest} = item;
            return children[0];
          }
          return item;
        });
        console.log(menuOptions, "menuOptions");
        this.menuOptions = menuOptions.map(item => {
          // 递归处理函数
          const transformNode = (node) => {
            return {
              label: node.meta.title,
              value: node.meta.menuId || node.path,
              children: node.children ? node.children.map(transformNode) : undefined
            };
          };
          return transformNode(item);
        });
      });
    },

    // 表单重置
    reset() {
      this.form = {
        noticeId: undefined,
        noticeTitle: undefined,
        noticeType: undefined,
        noticeContent: undefined,
        status: "0"
      };
      this.moreEntranceList  = [
        {entranceType: 'link'},
        {entranceType: 'affair'},
        {entranceType: 'shortcut'}
      ];
      this.resetForm("form");
    },
  }
}
</script>

<style scoped lang="scss">
.plus-img {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.del-box {
  height: 32px;
  line-height: 32px;
  cursor: pointer;
}
</style>
