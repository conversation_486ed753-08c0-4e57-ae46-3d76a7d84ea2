<template>
  <div class="main" ref="main">
    <div class="base_content" v-if="isRead('ruleForm')" ref="base_content">
      <div class="title"><i class="el-icon-info" /> 基础信息</div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="120px" size="medium" class="demo-ruleForm" style="margin-top: 10px">
        <el-row :gutter="15">
          <el-col :span="8" v-if="!isHide('workName')">
            <el-form-item label="通报名称" prop="workName" ref="workName">
              <el-input size="small" :disabled="!isWrite('ruleForm') || !isWrite('workName')" v-model.trim="ruleForm.workName" placeholder="请填写通报名称" maxlength="80" @blur="refreshWord" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isReadOrNull('period')" style="height: 59px">
            <el-form-item label="期号" prop="period" :disabled="!isWrite('ruleForm') || !isWrite('period')" :required="isRequired('period')">
              <el-input-number style="width: 100%" size="small" v-model="ruleForm.period" controls-position="right" :min="1" :max="999999" :disabled="!isWrite('ruleForm') || !isWrite('period')" @change="refreshWord"></el-input-number>
            </el-form-item>
          </el-col>
<!--          <el-col :span="8" v-if="isReadOrNull('issue')">
            <el-form-item label="期刊" prop="issue" :disabled="!isWrite('ruleForm') || !isWrite('issue')">
              <el-input :disabled="true" size="small" v-model.trim="ruleForm.issue" placeholder="" maxlength="80" @blur="refreshWord" />
            </el-form-item>
          </el-col>-->
          <el-col :span="8" v-if="isReadOrNull('remark6') || isWriteOrNull('remark6')">
            <el-form-item label="通报类型" prop="remark6" ref="remark6">
              <el-select size="small" v-model="ruleForm.remark6" placeholder="请选择通报类型" :disabled="!isWrite('ruleForm') || !isWrite('remark6')" @change="refreshWord">
                <el-option
                  v-for="item in reportOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isReadOrNull('urgency')">
            <el-form-item label="紧急程度" prop="urgency" :disabled="!isWrite('ruleForm') || !isWrite('urgency')">
              <el-select size="small" v-model="ruleForm.urgency" placeholder="请选择" :clearable="true" :disabled="!isWrite('ruleForm') || !isWrite('urgency')" @change="refreshWord">
                <el-option
                  v-for="item in urgencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isReadOrNull('severityLevel')">
            <el-form-item label="严重程度" prop="severityLevel" :disabled="!isWrite('ruleForm') || !isWrite('severityLevel')">
              <el-select size="small" v-model="ruleForm.severityLevel" placeholder="请选择" :clearable="true" :disabled="!isWrite('ruleForm') || !isWrite('severityLevel')" @change="refreshWord">
                <el-option
                  v-for="item in severityLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isReadOrNull('isPublic')">
            <el-form-item label="是否公开" prop="isPublic" :disabled="!isWrite('ruleForm') || !isWrite('isPublic')">
              <el-select size="small" v-model="ruleForm.isPublic" :default-first-option="true" placeholder="请选择" :clearable="true" :disabled="!isWrite('ruleForm') || !isWrite('isPublic')" @change="refreshWord">
                <el-option
                  v-for="item in isPublicOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="base_content" v-if="isRead('ruleForm')" ref="time_content">
      <div class="title"><i class="el-icon-time" /> 时间信息</div>
      <el-form :model="ruleForm" :rules="rules" ref="timeForm" label-width="120px" size="medium" class="demo-ruleForm" style="margin-top: 10px">
        <el-row :gutter="15">
<!--          <el-col :span="8" v-if="!isHide('eventCreateTime')">
            <el-form-item label="发现时间" prop="eventCreateTime" ref="eventCreateTime">
              <el-date-picker
                :disabled="!isWrite('ruleForm') || !isWrite('eventCreateTime')"
                size="small"
                style="width: 100%"
                v-model="ruleForm.eventCreateTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择时间">
              </el-date-picker>
            </el-form-item>
          </el-col>-->
          <el-col :span="8" v-if="isReadOrNull('reportDate')">
            <el-form-item label="通报日期" prop="reportDate" :disabled="!isWrite('ruleForm') || !isWrite('reportDate')">
              <el-date-picker
                v-model="ruleForm.reportDate"
                @change="refreshWord"
                type="date"
                style="width: 100%"
                :disabled="!isWrite('ruleForm') || !isWrite('reportDate')"
                value-format="yyyy-MM-dd"
                placeholder="请选择通报日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="!isHide('expectCompleteTime')">
            <el-form-item label="计划完成" prop="expectCompleteTime" ref="expectCompleteTime">
              <el-date-picker
                :disabled="!isWrite('ruleForm') || !isWrite('expectCompleteTime')"
                size="small"
                style="width: 100%"
                v-model="ruleForm.expectCompleteTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择日期">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="base_content" v-if="isRead('ruleForm')" ref="person_content">
      <div class="title"><i class="el-icon-user-solid" /> 人员信息</div>
      <el-form :model="ruleForm" :rules="rules" ref="personForm" label-width="120px" size="medium" class="demo-ruleForm" style="margin-top: 10px">
        <el-row :gutter="15">
          <el-col :span="8" v-if="isReadOrNull('signed')">
            <el-form-item label="签发" prop="signed" :disabled="!isWrite('ruleForm') || !isWrite('signed')" :required="isRequired('signed')">
              <el-input size="small" v-model="ruleForm.signed" :disabled="!isWrite('ruleForm') || !isWrite('signed')" @blur="refreshWord"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isReadOrNull('proofread')">
            <el-form-item label="核稿" prop="proofread" :disabled="!isWrite('ruleForm') || !isWrite('proofread')" :required="isRequired('proofread')">
              <el-input size="small" v-model="ruleForm.proofread" :disabled="!isWrite('ruleForm') || !isWrite('proofread')" @blur="refreshWord"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="isReadOrNull('editor')">
            <el-form-item label="编辑" prop="editor" :disabled="!isWrite('ruleForm') || !isWrite('editor')" :required="isRequired('editor')">
              <el-input size="small" v-model="ruleForm.editor" :disabled="!isWrite('ruleForm') || !isWrite('editor')" @blur="refreshWord"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 告知单 开始 -->
    <div class="ext_content" v-if="isRead('informForm')">
      <div class="title">
        <i class="el-icon-s-order" /> 通报内容
      </div>
      <el-form :model="informForm" :rules="informRules" ref="informForm" label-width="120px" size="medium" class="demo-informForm" style="margin-top: 10px">
<!--        <el-form-item label="事件来源" prop="eventSource" v-if="!isHide('eventSource')" ref="eventSource">
          <el-input size="small" v-model="informForm.eventSource" placeholder="请填写事件来源" maxlength="50" :disabled="!isWriteOrNull('eventSource')" />
        </el-form-item>
        <el-form-item label="事件类型" prop="eventType" v-if="!isHide('eventType')" ref="eventType">
          <el-cascader size="small" v-model="informForm.eventType" :options="threatenDict" clearable placeholder="请选择事件类型" style="width: 100%" :props="{ label: 'dictLabel', value: 'dictValue' }" :disabled="!isWriteOrNull('eventType')">
            <template slot-scope="{ node, data }">
              <span>{{ data.dictLabel }}</span>
              <span v-if="!node.isLeaf"> ({{ data.children.length }}) </span>
            </template>
          </el-cascader>
          &lt;!&ndash;          <el-select v-model="informForm.eventType" placeholder="请选择事件类型">
                      <el-option
                        v-for="item in eventTypeOption"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                      </el-option>
                    </el-select>&ndash;&gt;
        </el-form-item>
        <el-form-item label="级别" prop="eventLevel" v-if="!isHide('eventLevel')" ref="eventLevel">
          <el-select size="small" v-model="informForm.eventLevel" placeholder="请选择事件级别" :disabled="!isWriteOrNull('eventLevel')">
            <el-option
              v-for="item in eventLevelOption"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item label="事件通报" prop="eventNotification" ref="eventNotification" v-if="!isHide('eventNotification')" :required="isRequired('eventNotification')">
          <el-input size="small" type="textarea" v-model="informForm.eventNotification" :autosize="{ minRows: 5}" placeholder="请填写事件通报内容" show-word-limit maxlength="2000"
                    :disabled="!isWriteOrNull('eventNotification')" @blur="refreshWord"></el-input>
        </el-form-item>
        <el-form-item label="处理建议" prop="handleOpinion" ref="handleOpinion" v-if="!isHide('handleOpinion')">
          <el-input size="small" type="textarea" v-model="informForm.handleOpinion" :autosize="{ minRows: 5}" placeholder="请填写处理建议" show-word-limit maxlength="2000"
                    :disabled="!isWriteOrNull('handleOpinion')" @blur="refreshWord"></el-input>
        </el-form-item>
        <el-form-item label="描述附件" prop="describeFileUrl" ref="describeFileUrl" v-if="!isHide('describeFileUrl')">
          <file-upload v-model="informForm.describeFileUrl"
                       :disUpload="!isWrite('informForm') || !isWriteOrNull('describeFileUrl')"
                       :limit="5"
                       :file-type="['doc', 'docx', 'xlsx','xls', 'ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']"
                       @change="refreshWord"
          />
        </el-form-item>
        <el-form-item label="发" prop="remark8" ref="remark8" v-if="!isHideOrNull('remark8')" :show-message="false">
          <el-input size="small" type="text" v-model="informForm.remark8" placeholder="" show-word-limit maxlength="50" :disabled="!isWriteOrNull('remark8')" @blur="refreshWord"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <!-- 告知单 结束 -->

    <!-- 告知单审核签名签章 开始 -->
    <!--    <div class="ext_content" v-if="isRead('informFormSign')">
          <div class="title">
            签字签章
          </div>
        </div>-->
    <!-- 告知单审核签名签章 结束 -->

    <!-- 反馈单 开始 -->
    <div class="ext_content" v-if="isRead('feedbackForm')">
      <div class="title">
        <i class="el-icon-s-order" /> 反馈单信息
      </div>
      <el-form :model="feedbackForm" :rules="feedbackRules" ref="feedbackForm" label-width="120px" size="medium" class="demo-feedbackForm" style="margin-top: 10px">
        <el-form-item label="处置标题" prop="remark7" v-if="!isHideOrNull('remark7')" :required="isRequired('remark7')" :show-message="false">
          <el-input :disabled="!isWriteOrNull('remark7')" size="small" v-model="feedbackForm.remark7" placeholder="请输入处置标题" maxlength="50" @blur="refreshWord" />
        </el-form-item>
        <div style="display: flex;" v-if="!isWrite('feedbackForm')">
<!--          <el-form-item label="事件处理单文号" prop="workNo" v-if="!isHide('workNo')" style="width: 50%" ref="workNo">
            <el-input size="small" v-model="feedbackForm.workNo" placeholder="" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="反馈日期" prop="feedbackDate" v-if="!isHide('feedbackDate')" style="width: 50%" ref="feedbackDate">
            <el-input size="small" v-model="feedbackForm.feedbackDate" placeholder="" :disabled="!isWriteOrNull('feedbackDate')" @blur="refreshWord"></el-input>
          </el-form-item>-->
        </div>
        <el-form-item label="处理结果" prop="eventDescription" ref="eventDescription" v-if="!isHide('eventDescription')">
          <el-input size="small" type="textarea" v-model="feedbackForm.eventDescription" placeholder="请填写处理结果" show-word-limit maxlength="2000" :disabled="!isWriteOrNull('eventDescription')" @blur="refreshWord"></el-input>
        </el-form-item>
        <el-form-item label="处理情况" prop="handleSituation" ref="handleSituation" v-if="!isHide('handleSituation')">
          <el-input size="small" type="textarea" v-model="feedbackForm.handleSituation" placeholder="请填写处理情况" show-word-limit maxlength="2000" :disabled="!isWriteOrNull('handleSituation')" @blur="refreshWord"></el-input>
        </el-form-item>
        <el-form-item label="其他情况" prop="otherSituation" ref="otherSituation" v-if="!isHide('otherSituation')">
          <el-input size="small" type="textarea" v-model="feedbackForm.otherSituation" placeholder="请填写其他情况" show-word-limit maxlength="2000" :disabled="!isWriteOrNull('otherSituation')" @blur="refreshWord"></el-input>
        </el-form-item>
        <el-form-item label="附件" prop="feedbackFileUrl" ref="feedbackFileUrl" v-if="!isHide('feedbackFileUrl')">
          <file-upload v-model="feedbackForm.feedbackFileUrl"
                       :disUpload="!isWrite('feedbackForm') || !isWriteOrNull('feedbackFileUrl')"
                       :limit="5"
                       :file-type="['doc','docx', 'xls', 'xlsx','ppt','pptx', 'txt', 'pdf', 'png', 'jpg', 'jpeg','zip','rar','rar4']"
                       @change="refreshWord"
          />
        </el-form-item>
      </el-form>
    </div>
    <!-- 反馈单 结束 -->

    <!-- 通报对象 开始 -->
    <div class="ext_content" v-if="true">
      <div class="title">
        <i class="el-icon-aim" /> 通报对象
        <div class="title-right"><div style="cursor: pointer;" @click="addDept" v-if="setting && setting.opType === '-1'">+新增单位</div></div>
      </div>
      <report-target ref="reportTarget" :dept-data-list="reportTargetForm" :setting="setting" style="margin-top: 10px;" />
    </div>
    <!-- 通报对象 结束 -->

    <!-- 反馈单审核签名签章 开始 -->
    <!--    <div class="ext_content" v-if="isRead('feedbackFormSign')">
          <div class="title">
            签字签章
          </div>
        </div>-->
    <!-- 反馈单审核签名签章 结束 -->

    <!--  业务系统-资产选择  -->
    <el-dialog v-if="applicationDialog" title=选择资产 :visible.sync="applicationDialog" class="application_dialog" width="80%" append-to-body>
      <application-select v-if="applicationDialog" ref="applicationSelect" :value="currentApplicationSelect" :isMultipleSelect="true" :application-list="applicationList" @cancel="applicationDialog = false" @applicationSelected="applicationSelected" />
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitApplicationSelect">确 定</el-button>
        <el-button @click="applicationDialog = false">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog v-if="openEventDetailDialog" title="查看事件详情" :visible.sync="openEventDetailDialog" width="80%" append-to-body>
      <alarm-detail v-if="openEventDetailDialog && currentEventType == 3" @openDetail="openDetail" :asset-data="assetData"/>
      <new-addloophole v-if="openEventDetailDialog && currentEventType == 1" :loophole-data="assetData" :editable="false" @cancel="openEventDetailDialog=false" />
      <new-addwebvuln v-if="openEventDetailDialog && currentEventType == 2" :webvuln-data="assetData" :editable="false" @cancel="openEventDetailDialog=false" />
    </el-dialog>

    <!--  事件选择弹窗-开始  -->
    <select-event v-if="openEventSelectDialog" :selected-event-ids="ruleForm.eventIds" :dest-ips="ruleForm.associatedIps" :open.sync="openEventSelectDialog" :setting="setting" :threaten-dict="threatenDict" :current-event-type="parseInt(currentEventType)" :severity-options="severityOptions" @cancel="openEventSelectDialog=false" @selected="handleEventSelected"/>
    <!--  事件选择弹窗-结束  -->
  </div>
</template>

<script>
import { getAllUserListByDept,getAllUserList } from "../../../api/system/user"
import { addOrder } from "../../../api/tool/work";
import DeptSelect from "../../components/select/deptSelect";
import RelevancyGapInfo from "./relevancyGapInfo";
import RelevancyAlarmInfo from "./relevancyAlarmInfo";
import relevancyWebvulnInfo from "./relevancyWebvulnInfo";
import {listWebVuln} from "@/api/monitor2/webvuln"; //web漏洞
import {getVulnDealList} from "@/api/monitor2/assetFrailty"; //IP漏洞
import {listAlarm} from "@/api/threaten/threatenWarn"; //威胁事件
import comMixin from '@/views/zeroCode/workFlow/workFlowForm/mixin'
import {getMulTypeDict} from "@/api/system/dict/data";
import {getApplicationListByCondition,getBusinessInfo,getApplicationDetails} from "@/api/safe/application";
import { assetSelectList,getInfoByids,listByApplicationId } from "@/api/assetSelect/assetSelect.js";
import ApplicationSelect from "./applicationSelect.vue";
import SelectedEventList from "./selected_event_list.vue";
import SelectEvent from "./select_event.vue";
import AlarmDetail from "@/views/basis/securityWarn/alarmDetail.vue";
import newAddloophole from "@/views/frailty/loophole/newAddloophole.vue";
import newAddwebvuln from "@/views/frailty/webvuln/newAddwebvuln";
import reportTarget from "@/views/todoItem/todo/report_target.vue";
import {listDept} from "@/api/system/dept";

export default {
  name: "createWork",
  mixins: [comMixin],
  dicts: [
    'loophole_category',
    'threaten_alarm_type',
    'work_order_report_type',
    'work_order_urgency',
    'work_order_public',
    'work_order_severity_level',
  ],
  components: {AlarmDetail, ApplicationSelect, RelevancyAlarmInfo, RelevancyGapInfo, DeptSelect,relevancyWebvulnInfo,
    newAddloophole,newAddwebvuln,SelectedEventList,SelectEvent,reportTarget},
  props: {
    workType: {
      type: String,
      require: true
    },
    mId: {
      type: Number,
      require: true
    }
  },
  data() {
    return {
      componentName: 'createWork',
      scrollCache: null,
      openEventSelectDialog: false,
      assetData: {},
      openEventDetailDialog: false,
      currentEventType: 0,
      queryEventParams: {
        pageNum: 1,
        pageSize: 10
      },
      eventListLoading: false,
      eventList: [],
      total: 0,
      ruleForm: {
        workName: null,
        complateTime: null,
        handleDept: null,
        handleUser: null,
        workType: null,
        eventType: null,
        associatedIps: [],
      },
      dataForm: {
      },
      dataRule:{},
      selectedEvent: [],
      tempSelectedEvent: [],
      severityOptions: {
        '1': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},
        ],
        '2': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '低危', value: 1},{type: 'primary', label: '中危', value: 2},{type: 'warning', label: '高危', value: 3},{type: 'danger', label: '严重', value: 4},
        ],
        '3': [
          {type: 'info', label: '未知', value: 0},{type: 'success', label: '无威胁', value: 1},{type: 'primary', label: '低危', value: 2},{type: 'warning', label: '中危', value: 3},{type: 'warning', label: '高危', value: 4},{type: 'danger', label: '严重', value: 5},
        ],
      },
      threatenDict: [],
      rules: {
        workName: [
          { required: true, message: '请输入通报名称', trigger: 'blur' },
          { min: 3, max: 80, message: '长度在 3 到 80 个字符', trigger: 'blur' }
        ],
        applicationId: [
          { required: false, message: '请选择业务系统', trigger: 'change' }
        ],
        eventCreateTime: [
          { required: true, message: '请选择事件发生时间', trigger: 'change' }
        ],
        expectCompleteTime: [
          { required: true, message: '计划完成时间', trigger: 'change' }
        ],
        handleDept: [
          { required: true, message: '请选择处置单位', trigger: 'change' }
        ],
        handleUser: [
          { required: true, message: '请选择处置人', trigger: 'change' }
        ],
        handleUserPhone: [
          { required: false, message: '请输入处置人电话', trigger: 'blur' }
        ],
        remark6: [
          { required: true, message: '请选择通报类型', trigger: 'change' }
        ],
      },
      flowStateOptions: [
        {
          label: '待审核',
          value: 0
        },
        {
          label: '待处置',
          value: 1
        },
        {
          label: '待审核',
          value: 2
        },
        {
          label: '待验证',
          value: 3
        },
        {
          label: '已完成',
          value: 4
        },
        {
          label: '未分配',
          value: 99
        }
      ],
      tableData: [],
      alertTableData: [],
      handleOption: [],
      manageOption: [],
      handleOptionCopy: [],
      manageOptionCopy: [],
      informForm: {},
      informRules: {
        eventSource: [
          {required: true, message: '请输入事件来源', trigger: 'blur'}
        ],
        handleOpinion: [
          {required: false, message: '请输入处理建议', trigger: 'blur'}
        ],
        eventNotification: [
          {required: false, message: '请输入事件通报内容', trigger: 'blur'}
        ],
        describeFileUrl: [
          {required: false, message: '请上传描述附件', trigger: 'change'}
        ],
        /*remark8: [
          {required: true, message: '请输入', trigger: 'blur'}
        ],*/
      },
      feedbackForm: {},
      feedbackRules: {
        eventDescription: [
          {required: false, message: '请输入处理结果', trigger: 'blur'}
        ],
        handleSituation: [
          {required: false, message: '请输入处理情况', trigger: 'blur'}
        ]
      },
      applicationList: [],
      applicationDialog: false,
      macAipList: [],
      assetIds: [],
      currentApplicationSelect: null,
      eventTypeOption: [
        {
          label: '漏洞',
          value: 1
        },
        {
          label: '后门',
          value: 2
        },
        {
          label: '外链',
          value: 3
        }
      ],
      eventLevelOption: [
        {
          label: 'Ⅰ级',
          value: '1'
        },
        {
          label: 'Ⅱ级',
          value: '2'
        },
        {
          label: 'Ⅲ级',
          value: '3'
        },
        {
          label: 'Ⅳ级',
          value: '4'
        },
      ],
      applicationInfo: {},
      isChangeEventType: false,
      eventTypeBtnKey: 0,
      isFromEvent: false,
      isChangeForm: false,
      formOperatesRecord: [],
      reportTargetForm: [],
    }
  },
  watch: {
    informForm: {
      handler(newVal,oldVal){
        this.isChangeForm = true;
      },
      deep: true
    },
    ruleForm: {
      handler(newVal,oldVal){
        this.isChangeForm = true;
      },
      deep: true
    },
    feedbackForm: {
      handler(newVal,oldVal){
        this.isChangeForm = true;
      },
      deep: true
    },
    'ruleForm.handleDept': {
      deep: false,
      immediate: true,
      handler(val) {
        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){
          this.getUserList()
        }*/
        if(val){
          this.getUserList();
        }
      }
    },
    'ruleForm.manageDept': {
      deep: false,
      immediate: true,
      handler(val) {
        /*if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){
          this.getUserList()
        }*/
        if(val){
          this.getManageUserList();
        }
      }
    },
    'ruleForm.applicationId': {
      deep: true,
      immediate: true,
      handler(newVal,oldVal){
        if(newVal && newVal != oldVal){
          if(!this.setting.formData || Object.keys(this.setting.formData).length === 0){
            this.getApplicationDetails();
          }
        }
      }
    },
    'setting.row': {
      deep: true,
      immediate: true,
      handler(val){
        if(val && Object.keys(val).length>0){
          if(val.eventType && val.eventType != 0){
            this.currentEventType = val.eventType;
          }
          if(val.createTime){
            this.$set(this.ruleForm, 'eventCreateTime', val.createTime);
          }
          /*if(val.id){
            this.$set(this.ruleForm, 'eventIds', [val.id]);
          }*/
          let deptId = sessionStorage.getItem('deptId');
          let deptName = sessionStorage.getItem('deptName');
          if(val.deptId){
            deptId = val.deptId;
            deptName = val.deptName.split(',')[0];
          }
          this.reportTargetForm = [
            {
              deptName: deptName,
              deptId: deptId,
              formData: [{
                handleDept: deptId,
                handleUser: '',
                applicationId: val.businessApplications?val.businessApplications[0].assetId:null,
                assetName: val.businessApplications?val.businessApplications[0].assetName:null,
                loginUrl: val.businessApplications?val.businessApplications[0].url:null,
                manager: val.businessApplications?parseInt(val.businessApplications[0].manager):null,
                phone: val.businessApplications?val.businessApplications[0].managerPhone:null,
                eventData: {
                  'type1': val.eventType === 1?[
                    {type: val.eventType, eventId: val.id}
                  ]:[],
                  'type2': val.eventType === 2?[
                    {type: val.eventType, eventId: val.id}
                  ]:[],
                  'type3': val.eventType === 3?[
                    {type: val.eventType, eventId: val.id}
                  ]:[],
                  'type4': val.eventType === 4?[
                    {type: val.eventType, eventId: val.id}
                  ]:[]
                }
              }]
            }
          ];

          //默认查业务系统
          /*this.$nextTick(()=>{
            let ipv4 = this.getIpv4();
            if(ipv4){
              this.isFromEvent = true;
              getApplicationListByCondition({
                ipv4: ipv4,
                eventType: this.currentEventType,
                applicationId: this.setting && this.setting.row && this.setting.row.businessApplications && this.setting.row.businessApplications.length>0?this.setting.row.businessApplications[0].assetId:null
              }).then(res => {
                if(res.data && res.data.length>0){
                  let firstData = res.data[0];
                  listByApplicationId({
                    applicationId: firstData.assetId,
                    ip: ipv4,
                    eventType: this.currentEventType,
                    pageNum: 1,
                    pageSize: 1
                  }).then(assetRes => {
                    let assetData = [];
                    if(assetRes.rows && assetRes.rows.length>0){
                      assetData.push(assetRes.rows[0]);
                    }
                    let data = {
                      application: firstData,
                      selected: assetData
                    };
                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);
                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);
                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));
                    this.$set(this.ruleForm,'applicationId',data.application.assetId);
                    this.ruleForm.applicationName = data.application.assetName;
                    this.currentApplicationSelect = data;
                  })
                }
              })
            }
          })*/
        }
      }
    },
    'setting.rows': {
      deep: true,
      immediate: true,
      handler(val){
        if(val && val.length>0){
          if(val[0].eventType && val[0].eventType != 0){
            this.currentEventType = val[0].eventType;
          }
          if(val[0].createTime){
            this.$set(this.ruleForm, 'eventCreateTime', val[0].createTime);
          }
          let eventIds = val.map(item => item.id);
          this.$set(this.ruleForm, 'eventIds', eventIds);
          let row = val[0];
          this.reportTargetForm = [];
          let deptSet = new Set();
          let deptId = sessionStorage.getItem('deptId');
          let deptName = sessionStorage.getItem('deptName');
          val.forEach(item => {
            if(!item.deptId){
              item.deptId = deptId;
              item.deptName = deptName;
            }
            deptSet.add({
              deptName: item.deptName.split(',')[0],
              deptId: item.deptId,
            });
          })
          console.log(val)
          deptSet.forEach(item => {
            let matchArr = val.filter(valItem => valItem.deptId === item.deptId);
            let first = matchArr[0];
            this.reportTargetForm.push(
              {
                deptName: item.deptName,
                deptId: item.deptId,
                formData: [
                  {
                    handleDept: item.deptId,
                    applicationId: first.businessApplications?first.businessApplications[0].assetId:null,
                    assetName: first.businessApplications?first.businessApplications[0].assetName:null,
                    loginUrl: first.businessApplications?first.businessApplications[0].url:null,
                    manager: first.businessApplications?parseInt(first.businessApplications[0].manager):null,
                    phone: first.businessApplications?first.businessApplications[0].managerPhone:null,
                    eventData: {
                      'type1': this.currentEventType===1?matchArr.map(item => ({type: 1, eventId: item.id})):[],
                      'type2': this.currentEventType===2?matchArr.map(item => ({type: 2, eventId: item.id})):[],
                      'type3': this.currentEventType===3?matchArr.map(item => ({type: 3, eventId: item.id})):[],
                      'type4': this.currentEventType===4?matchArr.map(item => ({type: 4, eventId: item.id})):[]
                    }
                  }
                ]
              }
            )
          })
          /*//默认查业务系统
          this.$nextTick(()=>{
            let ipv4 = this.getIpv4();
            if(ipv4){
              this.isFromEvent = true;
              getApplicationListByCondition({
                ipv4: ipv4,
                eventType: this.currentEventType,
                applicationId: row.businessApplications && row.businessApplications.length>0?row.businessApplications[0].assetId:null
              }).then(res => {
                if(res.data && res.data.length>0){
                  let firstData = res.data[0];
                  listByApplicationId({
                    applicationId: firstData.assetId,
                    ip: ipv4,
                    eventType: this.currentEventType,
                    pageNum: 1,
                    pageSize: 1
                  }).then(assetRes => {
                    let assetData = [];
                    if(assetRes.rows && assetRes.rows.length>0){
                      assetData.push(assetRes.rows[0]);
                    }
                    let data = {
                      application: firstData,
                      selected: assetData
                    };
                    this.ruleForm.associatedIps = data.selected.map(item => item.ip);
                    this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);
                    this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));
                    this.$set(this.ruleForm,'applicationId',data.application.assetId);
                    this.ruleForm.applicationName = data.application.assetName;
                    this.currentApplicationSelect = data;
                  })
                }
              })
            }
          })*/
        }
      }
    },
    'setting.formData': {
      deep: true,
      immediate: true,
      handler(val){
        if(val && Object.keys(val).length !== 0){
          this.initForm();
        }
      }
    },
    'setting.flowTaskOperatorRecordList': {
      deep: true,
      immediate: true,
      handler(val){
        if(!this.setting.flowTemplateJson){
          return;
        }
        let allMatchNodes = [];
        this.loopGetFlowNode(this.setting.flowTemplateJson,'end',allMatchNodes);
        let allFormOperates = {};
        allMatchNodes.map(item => item.properties.formOperates).forEach(item => {
          item.forEach(arrItem => {
            if(!allFormOperates[arrItem.id]){
              allFormOperates[arrItem.id] = {
                read: arrItem.read,
                write: arrItem.write
              }
            }else {
              if(!allFormOperates[arrItem.id].read){
                allFormOperates[arrItem.id].read = arrItem.read;
              }
              if(!allFormOperates[arrItem.id].write){
                allFormOperates[arrItem.id].write = arrItem.write;
              }
            }
          })
        });
        this.formOperatesRecord = Object.entries(allFormOperates).map(([id, value]) => ({
          id: id,
          read: value.read,
          write: value.write
        }));
      }
    },
    currentEventType: {
      handler(newVal,oldVal){
        this.$set(this.ruleForm,'workType',newVal);
        /* if(newVal){
          this.$nextTick(() => {
            this.resetQuery();
          })
        } */
      }
    },
    'ruleForm.handleUser': {
      handler(newVal,oldVal){

      }
    },
    reportTargetForm: {
      deep: true,
      immediate: true,
      handler(newVal,oldVal){
        this.$emit('reportDataChange',newVal);
      }
    },
  },
  created() {
    getMulTypeDict({
      dictType:'threaten_alarm_type'
    }).then(res=>{
      this.threatenDict=res.data;
      // TODO  暂定为获取两层  当前方法有问题 其它版本也有修改需要等待后续修改
      /* if (this.threatenDict.length > 0 && this.threatenDict[0].children.length > 0) {
        this.ruleForm.eventType = this.threatenDict[0].dictValue + '/' + this.threatenDict[0].children[0].dictValue
      } */
      this.threatenDict.forEach(item=>{
        item.value=item.dictValue;
        item.label=item.dictLabel;
        item.children.forEach(cItem=>{
          cItem.value=cItem.dictValue;
          cItem.label=cItem.dictLabel;
        })
      })
    });
  },
  mounted() {
    this.$nextTick(() => {
      //默认值
      this.setFormDefault();
    })
  },
  computed: {
    categoryDict(){
      if(this.currentEventType == 1 || this.currentEventType == 2){
        return this.dict.type.loophole_category;
      }else if(this.currentEventType == 3){
        return this.threatenDict;
      }
    },
    reportOptions(){
      return this.dict.type.work_order_report_type;
    },
    urgencyOptions(){
      return this.dict.type.work_order_urgency;
    },
    isPublicOptions(){
      return this.dict.type.work_order_public;
    },
    severityLevelOptions(){
      return this.dict.type.work_order_severity_level;
    },
  },
  methods: {
    initForm(){
      this.setFormData(this.ruleForm, 'applicationId');
      this.currentApplicationSelect = {
        application: {
          assetId: this.setting.formData.applicationId,
        },
        selected: this.setting.formData.remark1?JSON.parse(this.setting.formData.remark1).map(se => {return {assetId: se}}):[],
      }
      this.setFormData(this.ruleForm, 'associatedIps');
      this.setFormData(this.ruleForm, 'eventIds');
      this.$set(this.queryEventParams,"ids",this.setting.formData.eventIds);
      this.setFormData(this.ruleForm, 'workType');
      this.currentEventType = this.setting.formData.workType;
      this.setFormData(this.ruleForm, 'workName');
      this.setFormData(this.ruleForm, 'loginUrl');
      this.setFormData(this.ruleForm, 'handleDept');
      this.setFormData(this.ruleForm, 'manageDept');
      this.setFormData(this.ruleForm, 'handleDeptName');
      this.setFormData(this.ruleForm, 'manageDeptName');
      this.setFormData(this.ruleForm, 'handleUser');
      this.$set(this.ruleForm,"manageUser",this.setting.formData.manageUser?parseInt(this.setting.formData.manageUser) : null);
      this.setFormData(this.ruleForm, 'handleUserPhone');
      this.setFormData(this.ruleForm, 'handleUserName');
      this.setFormData(this.ruleForm, 'manageUserName');
      this.setFormData(this.ruleForm, 'remark6');
      this.setFormData(this.ruleForm, 'issue');
      this.setFormData(this.ruleForm, 'expectCompleteTime');
      this.setFormData(this.ruleForm, 'eventCreateTime');
      this.setFormData(this.ruleForm, 'applicationName');
      this.$set(this.ruleForm,"urgency",this.setting.formData.urgency?this.setting.formData.urgency.toString() : '1');
      this.setFormData(this.ruleForm, 'isPublic');
      this.setFormData(this.ruleForm, 'severityLevel');
      this.setFormData(this.ruleForm, 'reportDate');
      this.setFormData(this.ruleForm, 'period');
      this.setFormData(this.ruleForm, 'signed');
      this.setFormData(this.ruleForm, 'proofread');
      this.setFormData(this.ruleForm, 'editor');

      this.setFormData(this.informForm, 'describeFileUrl');
      this.setFormData(this.informForm, 'eventLevel');
      this.setFormData(this.informForm, 'eventNotification');
      this.setFormData(this.informForm, 'eventSource');
      // this.setFormData(this.informForm, 'workType');
      this.setFormData(this.informForm, 'handleOpinion');
      this.setFormData(this.informForm, 'eventType');
      this.setFormData(this.informForm, 'remark8');
      this.setFormData(this.feedbackForm, 'eventDescription');
      this.setFormData(this.feedbackForm, 'handleSituation');
      this.setFormData(this.feedbackForm, 'otherSituation');
      this.setFormData(this.feedbackForm, 'workNo');
      this.setFormData(this.feedbackForm, 'feedbackDate');
      this.setFormData(this.feedbackForm, 'feedbackFileUrl');
      this.setFormData(this.feedbackForm, 'remark7');
      //通报对象赋值
      this.reportTargetForm = this.setting.formData.reportTargetForm;

      this.refreshWord();
    },
    setFormData(form, column){
      let data = this.setting.formData[column];
      if('eventType' === column){
        if(data && data.indexOf("/")!==-1){
          data = data.split("/");
        }
      }
      this.$set(form, column, data);
    },
    setFormDefault(){
      if(!this.ruleForm.urgency){
        this.$set(this.ruleForm,'urgency','1');
      }
      if(!this.ruleForm.reportDate){
        this.$set(this.ruleForm,'reportDate',new Date());
      }
      if(!this.ruleForm.signed){
        this.$set(this.ruleForm,'signed','叶琛');
      }
      if(!this.ruleForm.proofread){
        this.$set(this.ruleForm,'proofread','王亮');
      }
      if(!this.ruleForm.editor){
        this.$set(this.ruleForm,'editor','付扬');
      }
    },
    submitForm(formName) {
      const that = this
      this.$refs[formName].validate((valid) => {
        if (valid) {
          that.ruleForm.businessId = that.mId
          that.ruleForm.workType = that.workType
          addOrder(that.ruleForm).then(res => {
            if (res.code === 200) {
              this.$message({
                message: '通报创建成功',
                type: 'success'
              })
              that.resetForm()
            } else {
              this.$message.error('通报创建失败')
            }
          })
        } else {
          console.log('error submit!!');
          return false;
        }
      })
    },
    resetForm() {
      this.$emit('closeWork')
    },
    openFocus() {
      if (!this.ruleForm.handleDept) {
        this.$message.warning('请先选择处理部门！');
      }
    },
    handleQuery(){
      this.queryEventParams.pageNum = 1;
      this.total = 0;
      this.getEventDataList();
    },
    resetQuery(){
      this.queryEventParams={
        pageNum: 1,
        pageSize: this.queryEventParams.pageSize
      };
      this.getEventDataList();
    },
    handleEventDetail(row){
      this.assetData= {...row};
      this.openDetail(true);
    },
    openDetail(val){
      this.openEventDetailDialog = val;
    },
    eventBtnClick(type,evt){
      if(this.currentEventType != type && this.currentEventType != 0){
        this.$confirm('切换事件类型将清空之前选择的事件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.selectedEvent = [];
          this.ruleForm.eventIds = [];
          this.currentEventType = type;
          this.$refs.eventList && this.$refs.eventList.clearSelection();
          this.isChangeEventType = true;
        }).catch(() => {
          this.eventTypeBtnKey++;
        });
      }else {
        if(this.currentEventType != 0 && this.currentEventType == type){
          if(this.isFromEvent){
            //从事件发起，不允许取消
            return false;
          }
          this.$confirm('取消选中事件类型将清空之前选择的事件, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.eventList = [];
            this.total = 0;
            this.selectedEvent = [];
            this.ruleForm.eventIds = [];
            this.currentEventType = 0;
            this.$refs.eventList && this.$refs.eventList.clearSelection();
            this.isChangeEventType = true;
            const target = evt.target;
            if(target){
              if(target.nodeName === 'SPAN'){
                if(target.parentNode){
                  target.parentNode.blur();
                }
              }
              target.blur();
            }
          }).catch(() => {

          });
        }else {
          this.currentEventType = type;
        }
      }
    },
    flowStateFormatter(row, column, cellValue, index){
      let name = '未分配';
      let match = this.flowStateOptions.find(item => item.value==cellValue);
      if(match){
        name = match.label;
      }
      return name;
    },
    getEventDataList(){
      if(!this.currentEventType && (!this.dataForm.eventIds || this.dataForm.eventIds.length<1)){
        return false;
      }
      if(this.currentEventType == 0){
        return false;
      }
      this.eventListLoading = true;
      this.requestList().then(response => {
        let resData = this.convertEventData(response.rows);
        this.eventList = resData;
        this.total= response.total;
        this.eventListLoading = false;
      })
    },
    //转换
    convertEventData(srcDataList){
      if(!srcDataList){
        return [];
      }
      if(this.currentEventType == 3){
        return srcDataList.map(item => {
          return {
            id: item.id,
            title: item.threatenName,
            category: item.threatenType,
            severity: item.alarmLevel,
            webUrl: item.destIp,
            handleStatus: item.handleState,
            workState: item.orderState,
            dataSource: item.dataSource,
            scanNum: item.alarmNum,
            updateTime: item.updateTime,
            createTime: item.createTime,
            hostPort: item.destPort,
            flowState: item.flowState
          }
        })
      }else if(this.currentEventType == 1){
        return srcDataList.map(item => {
          item.webUrl = item.hostIp;
          return item;
        });
      }else {
        return srcDataList;
      }

    },
    requestList(){
      let queryParams = this.convertQueryParams(this.queryEventParams);
      if(this.currentEventType == 1){
        //IP漏洞事件
        return getVulnDealList(queryParams);
      }else if(this.currentEventType == 2){
        //应用漏洞事件
        return listWebVuln(queryParams);
      }else if(this.currentEventType == 3){
        //威胁事件
        return listAlarm(queryParams);
      }
    },
    convertQueryParams(srcParams){
      if(!this.isRead('list_select')){
        srcParams.ids = this.dataForm.eventIds;
      }
      if(this.currentEventType == 3){
        return {
          pageNum: srcParams.pageNum,
          pageSize: srcParams.pageSize,
          threatenName: srcParams.title,
          threatenType: srcParams.category?srcParams.category.join('/'):null,
          alarmLevel: srcParams.severity,
          destIp: srcParams.webUrl,
          destPort: srcParams.hostPort,
          handleState: srcParams.handleStatus,
          orderState: srcParams.workState,
          dataSource: srcParams.dataSource,
          createTime: srcParams.createTime,
          ids: srcParams.ids
        }
      }else if(this.currentEventType == 1){
        srcParams.hostIp = srcParams.webUrl;
        return srcParams;
      }else {
        return srcParams;
      }
    },
    handleEventSelected(val, event){
      if(!this.ruleForm.eventIds){
        this.ruleForm.eventIds = [];
      }
      let tempArr = [...this.ruleForm.eventIds];
      tempArr.push(...val);
      if (event) {
        this.ruleForm.eventType = event
      }
      this.$set(this.ruleForm,'eventIds',tempArr);
      this.selectedEvent = this.ruleForm.eventIds;
    },
    handleSeverityTag(severity,key){
      if(!severity){
        return '未知';
      }
      if(this.severityOptions[this.currentEventType.toString()]){
        let matchItem = this.severityOptions[this.currentEventType.toString()].find(item => item.value == severity);
        if(!matchItem){
          return '未知';
        }
        return matchItem[key];
      }
      return '';
    },
    openApplicationSelect(){
      /*getApplicationListByCondition({
        // ipv4: this.getIpv4()
        ipv4: null
      }).then(res=>{
        this.applicationList=res.data;
        this.applicationDialog = true;
      });*/
      this.applicationDialog = true;
    },
    getIpv4(){
      if(!this.setting.row && (!this.setting.rows || this.setting.rows.length<1)){
        return null;
      }
      let row = this.setting.row || this.setting.rows[0];
      if(this.currentEventType == 1){
        //IP漏洞事件
        return row.hostIp;
      }else if(this.currentEventType == 2){
        //应用漏洞事件
        return row.webUrl;
      }else if(this.currentEventType == 3){
        //威胁事件
        return row.destIp;
      }else if(this.currentEventType == 4){
        //弱口令漏洞
        return row.hostIp;
      }
    },
    applicationSelected(data){
      this.currentApplicationSelect = data;
      this.ruleForm.associatedIps = data.selected.map(item => item.ip);
      this.ruleForm.associatedIps = this.ruleForm.associatedIps.filter((item, index, self) => self.indexOf(item) === index);
      this.ruleForm.remark1 = JSON.stringify(data.selected.map(item => item.assetId));
      this.$set(this.ruleForm,'applicationId',data.application.assetId);
      this.ruleForm.applicationName = data.application.assetName;
      this.applicationDialog = false;
      this.refreshWord();
    },
    submitApplicationSelect(){
      this.$refs.applicationSelect.submit();
    },
    getApplicationDetails(){
      let _that = this;
      getApplicationDetails(this.ruleForm.applicationId).then(res => {
        if(res.data.url){
          this.$set(_that.ruleForm,'loginUrl',res.data.url);
        }
        if(res.data.dept_id){
          this.$set(_that.ruleForm,'handleDept',res.data.dept_id);
          this.$set(_that.ruleForm,'manageDept',res.data.dept_id);

          this.$nextTick(() => {
            this.getManageUserList();
          })
        }
        this.applicationInfo = res.data;
      })
    },
    eventTypeBtnDisable(){
      if("-1" == this.setting.opType){
        return false;
      }
      if(this.setting.row && this.setting.row.eventType){
        return true;
      }
      return false;
    },
    getManageUserList() {
      // this.ruleForm.handleUser = ''
      this.manageOption = []
      if (this.ruleForm.manageDept) {
        getAllUserList({
          // deptId: this.ruleForm.manageDept
        }).then(res => {
          if (res.code === 200) {
            this.manageOption = res.rows;
            this.manageOptionCopy = [...this.manageOption];
            this.$nextTick(() => {
              /*if(this.manageOption && !this.manageOption.find(manageUserItem => manageUserItem.userId == this.ruleForm.manageUser)){
                this.ruleForm.manageUser = '';
                this.ruleForm.handleUserPhone = '';
              }*/

              if(this.applicationInfo){
                if(this.applicationInfo.manager){
                  let manager = this.applicationInfo.manager.split(',')[0];
                  /*if(this.manageOption.find(manageUserItem => manageUserItem.userId == manager)){
                    // this.$set(this.ruleForm,'handleUser',parseInt(manager));
                    this.$set(this.ruleForm,'manageUser',parseInt(manager));
                    this.$forceUpdate();
                  }*/
                  this.$set(this.ruleForm,'manageUser',parseInt(manager));
                  this.$forceUpdate();
                }
                if(this.applicationInfo.phonenumber){
                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);
                }
              }
            })
          }
        })
      }
    },
    getUserList() {
      // this.ruleForm.handleUser = ''
      this.handleOption = []
      if (this.ruleForm.handleDept) {
        getAllUserListByDept({
          deptId: this.ruleForm.handleDept
        }).then(res => {
          if (res.code === 200) {
            this.handleOption = res.rows;
            this.handleOptionCopy = [...this.handleOption];
            this.$nextTick(() => {
              if(this.handleOption && !this.handleOption.find(handleUserItem => handleUserItem.userId == this.ruleForm.handleUser)){
                this.ruleForm.handleUser = '';
                this.$set(this.ruleForm,'handleUser','');
                // this.ruleForm.handleUserPhone = '';
              }

              if(this.applicationInfo){
                if(this.applicationInfo.manager){
                  let manager = this.applicationInfo.manager.split(',')[0];
                  if(this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){
                    if(this.handleOption && this.handleOption.find(handleUserItem => handleUserItem.userId == manager)){
                      this.$set(this.ruleForm,'handleUser',parseInt(manager));
                    }
                  }
                }
                if(this.applicationInfo.phonenumber){
                  this.$set(this.ruleForm,'handleUserPhone',this.applicationInfo.phonenumber);
                }
              }
            })
          }
        })
      }
    },
    manageUserFilter(val){
      if(val){
        this.manageOption = this.manageOptionCopy.filter(option => {
          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;
        });
      }else {
        this.manageOption = [...this.manageOptionCopy];
      }
    },
    handleUserFilter(val){
      if(val){
        this.handleOption = this.handleOptionCopy.filter(option => {
          return option.userName.indexOf(val) != -1 || option.nickName.indexOf(val) != -1;
        });
      }else {
        this.handleOption = [...this.handleOptionCopy];
      }
    },
    manageUserVisibleChange(){
      this.manageOption = [...this.manageOptionCopy];
    },
    handleUserVisibleChange(){
      this.handleOption = [...this.handleOptionCopy];
    },
    manageUserChange(row){
      if(row){
        let matchUser = this.manageOption.find(item => item.userId==row);
        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');
      }
    },
    handleUserChange(row){
      if(row){
        let matchUser = this.handleOption.find(item => item.userId==row);
        this.$set(this.ruleForm,'handleUserPhone',matchUser?matchUser.phonenumber:'');
      }
    },
    handleUserPhoneInput(){
      this.$forceUpdate();
    },
    validateAllForm() {
      if(this.isRead('list_select') && (this.currentEventType && this.currentEventType !== 0)){
        if(!this.ruleForm.eventIds || this.ruleForm.eventIds.length < 1){
          this.$modal.msgError('请选择关联事件');
          this.$refs.event_list && this.$refs.event_list.scrollIntoView();
          return new Promise((resolve, reject) => {
            reject();
          });
        }
      }

      let validateForms = this.getValidateForm();
      if(!validateForms || validateForms.length < 1){
        return new Promise((resolve, reject) => {
          resolve();
        });
      }
      return Promise.all(validateForms);
    },
    validateForm(formName){
      return new Promise((resolve, reject) => {
        if(!this.$refs[formName]){
          reject();
        }
        this.$refs[formName].validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    getValidateForm(){
      let res = [];
      // let activeForms = this.getActiveForm();
      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];
      if(activeForms && activeForms.length>0){
        activeForms.forEach(formName => {
          if(this.$refs[formName]){
            res.push(this.validateForm(formName));
          }
        })
      }
      let refReportTarget = this.$refs.reportTarget;
      if(refReportTarget){
        res.push(...refReportTarget.validate());
      }
      return res;
    },
    getActiveForm(){
      let res = [];
      let flowVariable = this.setting.flowVariable;
      if(flowVariable && flowVariable.length > 0){
        let matchItem = flowVariable.find(item => item.key == 'formNames');
        if(matchItem && matchItem.value){
          let names = matchItem.value.split(',');
          names.forEach(name => {
            res.push(name);
          })
        }
      }
      return res;
    },
    handleColumn(column){
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==column);
      if(matchOperate){
        return matchOperate.read;
      }else {
        return true;
      }
    },
    isRead(name){
      let formOperates = [];
      if(!this.setting.readonly){
        formOperates = this.setting.formOperates;
      }else {
        formOperates = this.formOperatesRecord;
      }
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.read;
      }else {
        return true;
      }
    },
    isReadOrNull(name){
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return false;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.read;
      }else {
        return false;
      }
    },
    isWriteOrNull(name){
      if(this.setting.readonly){
        return false;
      }
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return false;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.write;
      }else {
        return false;
      }
    },
    isWrite(name){
      if(this.setting.readonly){
        return false;
      }
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.write;
      }else {
        return true;
      }
    },
    isHideOrNull(name){
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.hide===true;
      }else {
        return true;
      }
    },
    isHide(name){
      let formOperates = this.setting.formOperates;
      if(!formOperates || formOperates.length < 1){
        return true;
      }
      let matchOperate = formOperates.find(item => item.id==name);
      if(matchOperate){
        return matchOperate.hide===true;
      }else {
        return false;
      }
    },
    beforeSubmit(){
      //动态表单
      this.dataForm = {};
      // let activeForms = this.getActiveForm();
      let activeForms = ['ruleForm','personForm','timeForm','informForm','feedbackForm'];
      if(activeForms && activeForms.length>0){
        activeForms.forEach(formName => {
          if(this.$refs[formName]){
            this.dataForm = {...this.dataForm, ...this[formName]};
          }
        })
      }
      if(this.setting.flowVariable){
        let match = this.setting.flowVariable.find(item => item.key == 'workNoPrefix');
        if(match){
          this.dataForm.workNoPrefix = match.value;
        }
      }
      /*      if (this.dataForm.eventType) {
              this.dataForm.eventType = this.dataForm.eventType.join('/');
            }*/
      if(this.dataForm && this.dataForm.eventType){
        if(Array.isArray(this.dataForm.eventType)){
          this.dataForm.eventType = this.dataForm.eventType.join('/');
        }
      }else {
        if(this.currentEventType === 4){
          //弱口令
          this.dataForm.eventType = '弱口令';
        }
      }

      //通报对象
      let reportTargetRef = this.$refs.reportTarget;
      if(reportTargetRef){
        this.dataForm.reportTargetForm = reportTargetRef.submitForm();
      }
      return this.dataForm;
    },
    isSelectAble(row,index){
      if(this.setting && (!this.setting.originType || (this.setting.originType && this.setting.originType != 'event')) && (this.setting.row && this.setting.row.eventIds && this.setting.row.eventIds.find(item => item == row.id))){
        return true;
      }
      return (!row.flowState || row.flowState == '99');
    },
    selectEventClick(){
      if(!this.currentEventType){
        this.$message.error('请先选择事件类型');
        return false;
      }
      this.openEventSelectDialog = true;
    },
    isRequired(prop){
      if(!this.formOperates){
        return false;
      }
      let match = this.formOperates.find(item => item.id === prop);
      if(match && match.required){
        return true;
      }
      return false;
    },
    refreshWord(){
      /* if(this.isChangeForm){
        this.isChangeForm = false;
        //暂存
        // this.$eventBus.$emit('sendWorkForm', this.beforeSubmit());
        this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();
      } */
      this.$parent.$parent.$parent && this.$parent.$parent.$parent.handleShowWord && this.$parent.$parent.$parent.handleShowWord();
    },
    sendDataForm(){
      //暂存
      return this.beforeSubmit();
    },
    loopGetFlowNode(treeData,nodeId,arr){
      if(!treeData){
        return;
      }
      arr.push({
        nodeId: treeData.nodeId,
        properties: treeData.properties,
        state: treeData.state,
        type: treeData.type
      })
      if(treeData.nodeId === nodeId){
        return;
      }else {
        return this.loopGetFlowNode(treeData.childNode,nodeId,arr);
      }
    },
    addDept(){
      this.$refs.reportTarget && this.$refs.reportTarget.addDept();
    },
  }
}
</script>

<style scoped>
@import "../../../styles/track.css";
</style>
<style lang="scss" scoped>
.main{
  background-color: #F2F4F8;

  > div{
    background-color: #ffffff;
    padding: 15px;
  }
  > div:not(:first-child){
    margin-top: 1.5vh;
  }

  .base_content{
    .ips{
      display: flex;
      > div{
        background-color: #E7F2FF;
        color: #0778FF;
        border-width: 1px;
        border-style: solid;
        border-color: #0778FF;
        border-radius: 3px;
        height: 32px;
        text-align: center;
        overflow: hidden;
        font-size: 0.5vw;
      }
      > div:not(:first-child){
        margin-left: 1%;
      }
      .ips_item{
        width: 31%;
      }
      .ips_item_overflow{
        width: 7%;
      }
    }
  }

  .event_type_body{
    > div:not(:first-child){
      margin-top: 1.5vh;
    }

    .event_type_select{
      display: flex;
      .label{
        align-content: center;
      }
      .event_type_btn{
        display: flex;
        margin-left: 20px;
        > .event_type_btn_item:not(:first-child){
          margin-left: 10px;
        }

        .btn_active{
          background: #1890ff;
          border-color: #1890ff;
          color: #FFFFFF;
        }
      }
    }
  }

  .title-right{
    float: right;
    font-size: 14px;
    color: #6c6c6c;
  }
}
</style>
