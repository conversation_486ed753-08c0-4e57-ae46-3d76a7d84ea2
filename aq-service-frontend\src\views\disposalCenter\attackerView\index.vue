<template>
  <div class="JNPF-common-layout">
    <OrgTree ref="OrgTree" @node-click="handleNodeClick"/>
    <div class="JNPF-common-layout-center">
      <el-row class="JNPF-common-search-box" :gutter="16">
        <el-form @submit.native.prevent>
          <el-col :span="6">
            <el-form-item label="最近告警时间">
              <!--              <el-input v-model="query.updateTime" placeholder="请输入" clearable></el-input>-->
              <el-date-picker
                v-model="rangeTime"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00', '23:59:59']"/>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="攻击者IP">
              <el-input v-model="query.attackIp" placeholder="请输入" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="地理位置">
              <el-select v-model="query.location" placeholder="请选择" clearable>
                <el-option v-for="(item, index) in locationOptions" :key="index"
                           :label="item.fullName" :value="item.id"
                           :disabled="item.disabled"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <template v-if="showAll">
            <el-col :span="6">
              <el-form-item label="阻断状态">
                <el-select v-model="query.blockStatus" placeholder="请选择">
                  <el-option label="正在阻断" :value="1"></el-option>
                  <el-option label="曾经阻断" :value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="6">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" @click="search()">查询</el-button>
              <el-button icon="el-icon-refresh-right" @click="reset()">重置</el-button>
              <el-button type="text" icon="el-icon-arrow-down" @click="showAll=true" v-if="!showAll">
                展开
              </el-button>
              <el-button type="text" icon="el-icon-arrow-up" @click="showAll=false" v-else>
                收起
              </el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div class="JNPF-common-layout-main JNPF-flex-main">
        <div class="JNPF-common-head">
          <div></div>
          <div class="JNPF-common-head-right">
            <el-button type="primary" :disabled="!multipleSelection.length" @click="handelBatchOut()">批量阻断
            </el-button>
            <el-button type="primary" icon="el-icon-download" @click="exportData">导出</el-button>
          </div>
        </div>
        <JNPF-table
          v-loading="listLoading"
          :data="list"
          :hasC="true"
          :hasNO="false"
          @selection-change="handleSelectionChange">
          <el-table-column
            prop="attackIp"
            label="攻击者IP"
            align="left"
            width="180"
          >
            <template slot-scope="scope">
              <div style="display: flex; align-items: center; justify-content: flex-start">
                <span>{{ scope.row.attackIp }}</span>
                <img v-if="scope.row.blockedStatus" style="width: 24px;margin-left: 10px"
                     src="@/assets/images/block.png" alt="">
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="riskLevel_name"
            label="告警等级"
            align="left"
          >
            <template slot-scope="scope">
              <el-tag
                :type="getRiskLevelType(scope.row.riskLevel_name)"
                v-if="scope.row.riskLevel_name"
              >
                {{ scope.row.riskLevel_name || '--' }}
              </el-tag>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="tableField105"
            label="攻击者标签"
            align="left"
            width="200"
            :show-overflow-tooltip="false">
            <!--            <template slot-scope="scope">
                          <el-tag v-for="(item, index) in scope.row.tableField105" :key="index"
                                  type="warning"
                                  :hit="item.hit"
                                  :closable="item.closable"
                                  @close="handleTagClose(item, scope.row)">
                            {{ item.tagName }}
                          </el-tag>
                        </template>-->
            <template slot-scope="scope">
              <span class="tag-name" v-for="(tag,index) in scope.row.tableField105"
                    :style="{ backgroundColor: tagBackgroundColor[index], color: tagColor[index], marginBottom: scope.row.tableField105.length > 0 ? '5px' : '0'}">{{
                  tag.tagName
                }}</span>
            </template>
          </el-table-column>
          <el-table-column label="地理位置" prop="location" algin="left">
            <template slot-scope="scope">
              {{ scope.row.location | dynamicText(locationOptions) }}
            </template>
          </el-table-column>
          <el-table-column
            prop="deviceId"
            label="所属设备" align="left" :formatter="formatDeviceId"
          >
            <template slot-scope="scope">
              <span>{{ formatDeviceId(scope.row, null, scope.row.deviceId) || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="victimIpNums_name"
            label="攻击目标IP数" align="left"
          >
            <!--            <template slot-scope="scope">
                          <el-button type="text" @click="openDipDetails(scope.row)">{{ scope.row.victimIpNums_name }}</el-button>
                        </template>-->
          </el-table-column>
          <el-table-column
            prop="attackTypeNums_name"
            label="命中规则数" align="left"
          >
            <!--            <template slot-scope="scope">
                          <el-button type="text" @click="openAttackTypeDetails(scope.row)">{{ scope.row.attackTypeNums_name }}</el-button>
                        </template>-->
          </el-table-column>
          <el-table-column
            prop="attackNums_name"
            label="告警数量" align="left"
          >
          </el-table-column>
          <el-table-column
            prop="startTime_name"
            label="最早告警时间" align="left"
          >
          </el-table-column>
          <el-table-column
            prop="updateTime_name"
            label="最近告警时间" align="left"
          >
          </el-table-column>
          <el-table-column label="操作"
                           fixed="right" width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="goDetail(scope.row.id, scope.row)">详情
              </el-button>
              <el-button type="text" @click="handleOutClick(scope.row)">阻断
              </el-button>
            </template>
          </el-table-column>
        </JNPF-table>
        <pagination
          :total="total"
          :page.sync="listQuery.currentPage"
          :limit.sync="listQuery.pageSize"
          @pagination="initData"/>
      </div>
    </div>

    <el-dialog
      title="攻击目标IP列表"
      :visible.sync="dipDrawerVisible"
      direction="rtl"
      size="50%">
      <el-table :data="dipDetailsData" v-loading="dipDrawerLoading">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column property="sip" label="攻击IP"></el-table-column>
        <el-table-column property="dip" label="目标IP" :render-header="dipRenderHeader">
          <template slot-scope="scope">
            <span>{{ scope.row.dip }}</span>
            <span style="color: #ADADAD;margin-left: 5px;"
                  v-if="scope.row.serverName">[{{ scope.row.serverName }}]</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="命中规则数列表"
      :visible.sync="threatenNameDrawerVisible"
      direction="rtl"
      size="50%">
      <el-table :data="threatenNameDetailsData" v-loading="threatenNameDrawerLoading">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column property="sip" label="攻击IP"></el-table-column>
        <el-table-column property="threatenName" label="[规则ID] 告警名称">
          <template slot-scope="scope">
            <span
              :class="scope.row.alarmLevel?scope.row.alarmLevel===4?'threatenName-error':scope.row.alarmLevel===3?'threatenName-warn':scope.row.alarmLevel===2?'threatenName-success':'':''">{{ scope.row.threatenName }}</span>
          </template>
        </el-table-column>
        <el-table-column property="threatenType" label="攻击类型"></el-table-column>
        <el-table-column property="count" label="告警数量"></el-table-column>
      </el-table>
    </el-dialog>

    <Detail
      v-if="detailVisible"
      ref="Detail"
      :alarm-level-options="alarmLevelOptions"
      @refresh="detailVisible=false"/>

    <BatchBlockDialog
      @refresh="initData"
      :block-list="blockList"
      :duration-options="blockingDuration"
      :blocking-dialog-visible.sync="blockingDialogVisible"/>

    <ExportBox v-if="exportBoxVisible" ref="ExportBox" @download="download"/>
  </div>
</template>

<script>

import request from '@/utils/request'
import {mapGetters} from "vuex";
import {getDictionaryDataSelector} from '@/api/systemData/dictionary'
import OrgTree from "@/views/system/monitorbssvulndeal/OrgTree.vue";
import columnList from './columnList'
import Detail from "./Detail.vue";
import ExportBox from "./ExportBox.vue";
import BatchBlockDialog from "@/views/system/basethreatenalarm/BatchBlockDialog.vue";
import disposalCenter from "@/mixins/generator/disposalCenter";
import {getDipDetails, getThreatenNameDetails} from "@/api/disposalCenter/attackerView";
import jnpf from "@/utils/jnpf";

export default {
  mixins: [disposalCenter],
  components: {BatchBlockDialog, Detail, OrgTree, ExportBox},
  data() {
    return {
      showAll: false,
      query: {
        updateTime: undefined,
        attackIp: undefined,
        location: undefined,
        riskLevel: undefined,
      },
      rangeTime: [],
      deviceId: '',
      treeProps: {
        children: 'children',
        label: 'fullName',
        value: 'id',
        isLeaf: 'isLeaf'
      },
      columnList,
      list: [
        {
          attackIp: '***********',
          riskLevel: '高危',
          tagName: '标签1',
          location: '内网',
          victimIpNums: '1',
          attackTypeNums: '1',
          attackNums: '1',
          startTime: '2020-01-01 00:00:00',
          updateTime: '2020-01-01 00:00:00',
        }
      ],
      listLoading: false,
      total: 0,
      listQuery: {
        superQueryJson: '',
        currentPage: 1,
        pageSize: 20,
        sort: "desc",
        sidx: "",
      },
      expandObj: {},
      detailVisible: false,
      exportBoxVisible: false,
      riskLevelProps: {"label": "fullName", "value": "enCode"},
      locationOptions: [{"fullName": "内网", "id": "1"}, {"fullName": "外网", "id": "2"}, {
        "fullName": "内/外网",
        "id": "3"
      }],
      locationProps: {"label": "fullName", "value": "id"},
      interfaceRes: {},
      multipleSelection: [],
      blockingDuration: [],
      blockingDialogVisible: false, // 批量阻断弹窗
      blockList: [],
      equipmentInfoList: [],
      dipDrawerVisible: false,
      dipDetailsData: [],
      dipDrawerLoading: false,
      threatenNameDrawerVisible: false,
      threatenNameDetailsData: [],
      threatenNameDrawerLoading: false,
      tagColor: ['#c86c00', '#bf1a1a', '#1a2bbf', '#901abf'],
      tagBackgroundColor: ['#ff9f1933', '#fd828233', '#7899e033', '#c278e033'],
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    menuId() {
      return this.$route.meta.modelId || ''
    }
  },
  created() {
    this.getColumnList();
    this.initSearchDataAndListData()
  },
  methods: {
    // 详情
    goDetail(id, row) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.Detail.init(id, row)
      })
    },

    // 阻断
    handleOutClick(row) {
      this.blockList = [{
        ...row,
        srcIp: row.attackIp,
      }]
      this.blockingDialogVisible = true;
    },

    openDipDetails(row) {
      this.dipDetailsData = [];
      this.dipDrawerVisible = true;
      this.dipDrawerLoading = true;
      getDipDetails({
        attackIp: row.attackIp,
        startTime: row.startTime,
        updateTime: row.updateTime,
        deviceId: row.deviceId
      }).then(res => {
        this.dipDetailsData = res.data;
      }).finally(() => {
        this.dipDrawerLoading = false;
      })
    },

    openAttackTypeDetails(row) {
      this.threatenNameDetailsData = [];
      this.threatenNameDrawerVisible = true;
      this.threatenNameDrawerLoading = true;
      getThreatenNameDetails({
        attackIp: row.attackIp,
        startTime: row.startTime,
        updateTime: row.updateTime,
        deviceId: row.deviceId
      }).then(res => {
        this.threatenNameDetailsData = res.data;
      }).finally(() => {
        this.threatenNameDrawerLoading = false;
      })
    },


    dipRenderHeader(h, {column, $index}) {
      return h('div', [
        h('span', column.label),
        h('span', {
          style: {
            marginLeft: '5px',
            color: '#ADADAD'
          }
        }, '[主机名称]')
      ])
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },

    async initSearchDataAndListData() {
      await this.initSearchData()
      this.initData()
    },
    //初始化查询的默认数据
    async initSearchData() {
      await getDictionaryDataSelector('blockingTimeType').then(res => {
        this.blockingDuration = res.data.list
      })
    },

    getColumnList() {
      // 没有开启权限
      this.columnOptions = this.transformColumnList(this.columnList)
    },
    transformColumnList(columnList) {
      let list = []
      for (let i = 0; i < columnList.length; i++) {
        const e = columnList[i];
        if (!e.prop.includes('-')) {
          list.push(e)
        } else {
          let prop = e.prop.split('-')[0]
          let label = e.label.split('-')[0]
          let vModel = e.prop.split('-')[1]
          let newItem = {
            align: "center",
            jnpfKey: "table",
            prop,
            label,
            children: []
          }
          e.vModel = vModel
          if (!this.expandObj.hasOwnProperty(`${prop}Expand`)) this.$set(this.expandObj, `${prop}Expand`, false)
          if (!list.some(o => o.prop === prop)) list.push(newItem)
          for (let i = 0; i < list.length; i++) {
            if (list[i].prop === prop) {
              list[i].children.push(e)
              break
            }
          }
        }
      }
      this.getExportList(list)
      return list
    },

    getExportList(list) {
      let exportList = []
      for (let i = 0; i < list.length; i++) {
        if (list[i].jnpfKey === 'table') {
          for (let j = 0; j < list[i].children.length; j++) {
            exportList.push(list[i].children[j])
          }
        } else {
          exportList.push(list[i])
        }
      }
      this.exportList = exportList
    },

    handelBatchOut() {
      let uniqueArr = Array.from(
        new Map(this.multipleSelection.map(item => [item.attackIp + item.deviceId, item])).values()
      );
      this.blockList = uniqueArr.map(item => {
        return {
          ...item,
          srcIp: item.attackIp,
        }
      });
      this.blockingDialogVisible = true;
    },

    initData() {
      this.listLoading = true;
      let _query = {
        ...this.listQuery,
        ...this.query,
        keyword: this.keyword,
        dataType: 0,
        menuId: this.menuId,
        moduleId: '712601124267472261'
      };
      request({
        url: `/api/aq-service/attackAlarm/getList`,
        method: 'post',
        data: _query
      }).then(res => {
        var _list = [];
        for (let i = 0; i < res.data.list.length; i++) {
          let _data = res.data.list[i];
          _list.push(_data)
        }
        this.list = _list.map(o => ({
          ...o,
          ...this.expandObj,
        }))
        this.total = res.data.pagination.total
        this.listLoading = false
      })
    },
    handleDel(id) {
      this.$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        type: 'warning'
      }).then(() => {
        request({
          url: `/api/aq-service/Tbl_attack_alarm/${id}`,
          method: 'DELETE'
        }).then(res => {
          this.$message({
            type: 'success',
            message: res.msg,
            onClose: () => {
              this.initData()
            }
          });
        })
      }).catch(() => {
      });
    },

    exportData() {
      this.exportBoxVisible = true
      this.$nextTick(() => {
        this.$refs.ExportBox.init(this.exportList)
      })
    },

    download(data) {
      let query = {...data, ...this.listQuery, ...this.query, menuId: this.menuId}
      request({
        url: `/api/aq-service/attackAlarm/Actions/Export`,
        method: 'post',
        data: query
      }).then(res => {
        if (!res.data.url) return
        this.jnpf.downloadFile(res.data.url)
        this.$refs.ExportBox.visible = false
        this.exportBoxVisible = false
      })
    },

    getRiskLevelType(val) {
      switch (val) {
        case '未知':
          return 'info'; // 未知
        case '无威胁':
          return 'info'; // 无威胁
        case '低风险':
          return 'success'; // 中危
        case '中风险':
          return 'warning'; // 高危
        case '高风险':
          return 'danger'; // 严重
        default:
          return ''; // 默认蓝色
      }
    },

    handleNodeClick(data) {
      if (data.id === 'root') {
        this.query.deviceId = ''
        return this.initData();
      }
      this.query.deviceId = data.id;
      this.deviceId = data.id;
      this.search()
    },

    search() {
      this.listQuery.currentPage = 1
      this.listQuery.pageSize = 20
      this.listQuery.sort = "desc"
      this.listQuery.sidx = ""
      this.query.startTime = this.rangeTime ? jnpf.toDate(this.rangeTime[0]) : ''
      this.query.endTime = this.rangeTime ? jnpf.toDate(this.rangeTime[1]) : ''
      this.initData()
    },
    refresh(isrRefresh) {
      if (isrRefresh) this.reset()
    },
    reset() {
      this.rangeTime = [];
      for (let key in this.query) {
        this.query[key] = undefined
      }
      this.search()
    },

  }
}
</script>
<style scoped lang="scss">
.tag-name {
  display: inline-block;
  padding: 2px 12px;
  margin-bottom: 5px;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857143;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  background-image: none;
  border: 1px solid transparent;
  border-radius: 4px;
}
</style>

