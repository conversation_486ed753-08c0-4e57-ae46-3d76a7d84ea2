package com.ruoyi.work.domain;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import com.ruoyi.common.core.domain.entity.SysUser;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工单主表对象 tbl_work_order
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Data
public class TblWorkOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 工单编号 */
    private String workNo;

    /** 工单名称 */
    @Excel(name = "工单名称")
    private String workName;

    /** 工单类型（0：漏洞，1：威胁） */
    @Excel(name = "工单类型", readConverterExp = "0=漏洞修复,1=告警修复")
    private String workType;

    /** 关联事件id */
    private List<String> eventIds;

    /** 关联事件名称 */
    private List<String> eventNameList;

    /** 对应系统id */
    private String applicationId;

    /** 系统登录地址 */
    private String loginUrl;

    /**
     * 关联资产id
     */
    private List<String> assetIds;

    /**
     * 关联ip
     */
    private List<String> associatedIps;

    private String associatedIpsStr;

    /**
     * 发现时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date eventCreateTime;

    private String eventCreateTimeStr;

    /**
     * 处理部门
     */
    private Long handleDept;

    /**
     * 处理用户
     */
    private Long handleUser;

    /**
     * 处理用户联系电话
     */
    private String handleUserPhone;

    /**
     * 计划完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date expectCompleteTime;

    /**
     * 事件来源
     */
    private String eventSource;

    /**
     * 事件类型 1=IP 2=Web 3=威胁 4=弱口令
     */
    private String eventType;

    /**
     * 事件级别
     */
    private String eventLevel;

    /**
     * 处理建议
     */
    private String handleOpinion;

    /**
     * 事件通报内容
     */
    private String eventNotification;

    /**
     * 事件描述
     */
    private String eventDescription;

    /**
     * 处理情况
     */
    private String handleSituation;

    /**
     * 其他情况
     */
    private String otherSituation;

    /**
     * 反馈时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date feedbackDate;

    private String feedbackDateStr;

    /**
     * 反馈附件
     */
    private String feedbackFileUrl;

    /**
     * 描述附件
     */
    private String describeFileUrl;

    /**
     * 告知单签名
     */
    private String informSign;

    /**
     * 告知单印章
     */
    private String informSeal;

    /**
     * 反馈单签名
     */
    private String feedbackSign;

    /**
     * 反馈单印章
     */
    private String feedbackSeal;

    /**
     * 备用字段1
     */
    private String remark1;

    /**
     * 备用字段2
     */
    private String remark2;

    /**
     * 备用字段3
     */
    private String remark3;

    /**
     * 备用字段4
     */
    private String remark4;

    /**
     * 备用字段5
     */
    private String remark5;
    private String remark6;
    private String remark7;
    private String remark8;

    /** 紧急程度 字典work_order_urgency */
    private Integer urgency;

    /** 是否公开 字典work_order_public */
    private String isPublic;

    /** 严重程度 work_order_severity_level */
    private String severityLevel;
    /** 通报时间 */
    private Date reportDate;
    /** 期号 */
    private Integer period;
    /** 签发 */
    private String signed;
    /** 核稿 */
    private String proofread;
    /** 编辑 */
    private String editor;

    /** 实际完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /**
     * 流程id
     */
    private String fFlowid;

    /**
     * 工作流示例id(taskId)
     */
    private String prodId;

    /**
     * 流程状态
     */
    private String flowState;
    private List<String> flowStateList;

    /**
     * 完成工单人员
     */
    private String completeUser;

    /**
     * 责任人
     */
    private String manageUser;
    private String manageUserName;

    /**
     * 责任部门
     */
    private String manageDept;
    private String manageDeptName;

    /**
     * 期刊
     */
    private String issue;

    private JSONObject flowInfo;

    private Long waitDoneBy;

    private Date complateDateStart;
    private Date complateDateEnd;
    private Date overTrueDate;
    private Date overFalseDate;
    private String handleUserName;
    private String handleDeptName;
    //private String manageDeptName;
    private String workNoPrefix;
    private String applicationName;
    private String assetIps;
    private Integer isOver;

    private String workTypeName;

    /**
     * 导出WORD类别（fkd-反馈单，gzd-告知单）
     */
    private String wordType;

    private String eventLevelName;
    private String flowHandleUser;

    private String createTimeStr;

    private Boolean isOvertime;
    private String overtimeStr;

    private Integer queryState;
    private String handleState;
    private Date startCreateTime;
    private Date endCreateTime;
    private String handleUserStr;
    private String handleDeptStr;
    private Integer findCarbonCopyStatus;

    private String isTrue;

    private String nodeProperties;

    private List<String> historyNodeProperties;

    private String ftlName;

    private Integer queryFlowStatus;

    private Boolean isAllData;

    private Boolean isSelf;

    private List<SysUser> currentHandleUserList;

    private List<Long> handleDeptIds;
    private Long currentUserId;
    private Long notCurrentUserId;
    private Boolean queryAll;
    private Boolean onlySelf; //是否仅查自己
    private String type;

    private List<JSONObject> reportTargetForm;

    //当前通报对象部门
    private Long curTargetDept;

    public void setWorkType(String workType) {
        this.workType = workType;
        if(StrUtil.isNotBlank(workType)){
            if("1".equals(workType)){
                this.workTypeName = "漏洞事件";
            }
            if("2".equals(workType)){
                this.workTypeName = "Web漏洞";
            }
            if("3".equals(workType)){
                this.workTypeName = "威胁告警";
            }
            if("4".equals(workType)){
                this.workTypeName = "弱口令";
            }
        }
    }

    public void setEventLevel(String eventLevel) {
        this.eventLevel = eventLevel;
        if(StrUtil.isNotBlank(eventLevel)){
            if("1".equals(eventLevel)){
                eventLevelName = "I级";
            }
            if("2".equals(eventLevel)){
                eventLevelName = "II级";
            }
            if("3".equals(eventLevel)){
                eventLevelName = "III级";
            }
            if("4".equals(eventLevel)){
                eventLevelName = "IV级";
            }
        }
    }

    public void setAssociatedIps(List<String> associatedIps) {
        this.associatedIps = associatedIps;
        if(CollUtil.isNotEmpty(associatedIps)){
            associatedIpsStr = CollUtil.join(associatedIps, ",");
        }
    }

    public void setEventCreateTime(Date eventCreateTime) {
        this.eventCreateTime = eventCreateTime;
        if(eventCreateTime != null){
            eventCreateTimeStr = DateUtil.format(eventCreateTime, "yyyy-MM-dd");
        }
    }

    public void setFeedbackDate(Date feedbackDate) {
        this.feedbackDate = feedbackDate;
        if(feedbackDate != null){
            feedbackDateStr = DateUtil.format(feedbackDate, "yyyy-MM-dd");
        }
    }
}
