package com.ruoyi.work.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.wisewe.docx4j.convert.builder.document.DocumentConvertType;
import cn.wisewe.docx4j.convert.builder.document.DocumentConverter;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.Page;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysDictData;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.enums.FlowNotifyType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MinioUtil;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.bean.BeanUtil;
import com.ruoyi.common.utils.uuid.RandomUtil;
import com.ruoyi.dimension.domain.TblAssetFile;
import com.ruoyi.framework.websocket.WebSocketService;
import com.ruoyi.monitor2.domain.MonitorBssVulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWebvulnDeal;
import com.ruoyi.monitor2.domain.MonitorBssWpDeal;
import com.ruoyi.monitor2.service.IMonitorBssVulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssWebvulnDealService;
import com.ruoyi.monitor2.service.IMonitorBssWpDealService;
import com.ruoyi.rabbitmq.domain.SyncMessage;
import com.ruoyi.rabbitmq.enums.DataTypeEnum;
import com.ruoyi.rabbitmq.enums.OperationTypeEnum;
import com.ruoyi.rabbitmq.service.IHandleDataSyncSender;
import com.ruoyi.safe.domain.dto.OverviewParams;
import com.ruoyi.safe.service.impl.TblMessageServiceImpl;
import com.ruoyi.system.domain.SysNotice;
import com.ruoyi.system.domain.SysRoleDept;
import com.ruoyi.system.mapper.SysRoleDeptMapper;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.ISysDictDataService;
import com.ruoyi.system.service.ISysNoticeService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.threaten.domain.TblThreatenAlarm;
import com.ruoyi.threaten.service.ITblThreatenAlarmService;
import com.ruoyi.work.domain.*;
import com.ruoyi.work.mapper.TblWorkOrderMapper;
import com.ruoyi.work.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getUserId;
import static com.ruoyi.common.utils.SecurityUtils.getUsername;

/**
 * 工单主表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-14
 */
@Service
@Slf4j
public class TblWorkOrderServiceImpl implements ITblWorkOrderService {
    @Autowired
    private TblWorkOrderMapper tblWorkOrderMapper;

    @Autowired
    private ITblWorkBacklogService iTblWorkBacklogService;

    @Autowired
    private ITblWorkHistoryService iTblWorkHistoryService;

    @Autowired
    private Snowflake snowflake;

    @Autowired
    private MinioUtil minioUtil;

    @Autowired
    private TblMessageServiceImpl tblMessageService;
    @Resource
    private ISysNoticeService sysNoticeService;
    @Resource
    private ISysUserService userService;
    @Resource
    private IMonitorBssVulnDealService monitorBssVulnDealService;
    @Resource
    private IMonitorBssWebvulnDealService monitorBssWebvulnDealService;
    @Resource
    private ITblThreatenAlarmService threatenAlarmService;
    @Resource
    private IMonitorBssWpDealService monitorBssWpDealService;
    @Resource
    private SysRoleDeptMapper roleDeptMapper;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private IHandleDataSyncSender handleDataSyncSender;
    @Resource
    private ITblWorkOrderCommonWordsService workOrderCommonWordsService;
    @Resource
    private ISysDictDataService sysDictDataService;
    @Resource
    private ITblWorkOrderDeptService workOrderDeptService;
    @Resource
    private ITblWorkOrderEventService workOrderEventService;
    @Resource
    private ITblWorkOrderTargetService workOrderTargetService;

    private final ConcurrentHashMap<String, Lock> lockPool = new ConcurrentHashMap<>();

    private static final Map<String,String> flowStateMap = new HashMap<>();
    static {
        flowStateMap.put("1","待处置");
        flowStateMap.put("2","待审核");
        flowStateMap.put("0","待审核");
        flowStateMap.put("3","待核验");
        flowStateMap.put("4","已完成");
        flowStateMap.put("-1","待发起");
    }

    private static final Map<String,Integer> workEventTypeMap = new HashMap<>();
    static {
        workEventTypeMap.put("type1",1);
        workEventTypeMap.put("type2",2);
        workEventTypeMap.put("type3",3);
        workEventTypeMap.put("type4",4);
    }

    /**
     * 查询工单主表
     *
     * @param id 工单主表主键
     * @return 工单主表
     */
    @Override
    public TblWorkOrder selectTblWorkOrderById(Long id) {
        TblWorkOrder tblWorkOrder = tblWorkOrderMapper.selectTblWorkOrderById(id);
        /*if(StrUtil.isNotBlank(tblWorkOrder.getManageUser())){
            SysUser manageUserInDB = userService.selectUserById(Long.valueOf(tblWorkOrder.getManageUser()));
            if(manageUserInDB != null){
                tblWorkOrder.setManageUserName(manageUserInDB.getNickName());
            }
        }*/
        //附件
        getFileUrls(tblWorkOrder);
        //通报对象
        if(tblWorkOrder != null){
            List<TblWorkOrder> list = CollUtil.toList(tblWorkOrder);
            fillReportForm(list);
        }
        return tblWorkOrder;
    }

    /**
     * 批量查询工单主表
     *
     * @param ids 工单主表主键集合
     * @return 工单主表集合
     */
    @Override
    public List<TblWorkOrder> selectTblWorkOrderByIds(Long[] ids) {
        return tblWorkOrderMapper.selectTblWorkOrderByIds(ids);
    }

    /**
     * 查询工单主表列表
     *
     * @param tblWorkOrder 工单主表
     * @return 工单主表
     */
    @Override
    public List<TblWorkOrder> selectTblWorkOrderList(TblWorkOrder tblWorkOrder) {
        List<TblWorkOrder> tblWorkOrders = tblWorkOrderMapper.selectTblWorkOrderList(tblWorkOrder);
        for (TblWorkOrder workOrder : tblWorkOrders) {
            //附件
            getFileUrls(workOrder);
        }
        return tblWorkOrders;
    }

    private void getFileUrls(TblWorkOrder workOrder) {
        if (ObjectUtils.isNotEmpty(workOrder)) {
            TblAssetFile tblAssetFile = new TblAssetFile();
            tblAssetFile.setAssetId(workOrder.getId());
            /*if ("1".equals(workOrder.getState()) || "2".equals(workOrder.getState())) {
                tblAssetFile.setContent("处置");
                List<TblAssetFile> tblAssetFiles = tblAssetFileService.selectTblAssetFileList(tblAssetFile);
                String urls = tblAssetFiles.stream().map(TblAssetFile::getUrl).collect(Collectors.joining("|"));
                workOrder.setHandleUrlFiles(urls);
            }
            if ("2".equals(workOrder.getState())) {
                tblAssetFile.setContent("验证");
                List<TblAssetFile> tblAssetFiles = tblAssetFileService.selectTblAssetFileList(tblAssetFile);
                String urls = tblAssetFiles.stream().map(TblAssetFile::getUrl).collect(Collectors.joining("|"));
                workOrder.setVerifyUrlFiles(urls);
            }*/
        }
    }


    /**
     * 新增工单主表
     *
     * @param tblWorkOrder 工单主表
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertTblWorkOrder(TblWorkOrder tblWorkOrder) {
        Long workID = snowflake.nextId();
        if(StrUtil.isBlank(tblWorkOrder.getWorkName())){
            return 1;
        }
        // 插入id
        tblWorkOrder.setWorkNo(StrUtil.format("{}{}{}",StrUtil.isNotBlank(tblWorkOrder.getWorkNoPrefix())?tblWorkOrder.getWorkNoPrefix():"",
                DateUtil.format(DateUtil.date(), "yyyyMMdd"), RandomUtil.getRandomString(12).toUpperCase()));
        tblWorkOrder.setId(workID);
        tblWorkOrder.setCreateBy(getUsername());
        tblWorkOrder.setCreateTime(DateUtils.getNowDate());
        tblWorkOrder.setRemark5(getUserId().toString());
        tblWorkOrder.setUpdateTime(tblWorkOrder.getCreateTime());

        JSONObject flowInfo = tblWorkOrder.getFlowInfo();
        if(flowInfo != null){
            List<JSONObject> flowVariable = flowInfo.getList("flowVariable",JSONObject.class);
            if(CollUtil.isNotEmpty(flowVariable)){
                flowVariable.stream().filter(variable -> "state".equals(variable.getString("key")))
                        .findFirst().ifPresent(variable -> tblWorkOrder.setFlowState(variable.getString("value")));
            }
        }
        if(tblWorkOrder.getFlowInfo() != null && tblWorkOrder.getFlowInfo().containsKey("handleUser")){
            String nextNodeId = flowInfo.getString("nextNodeId");
            List<TblWorkBacklog> workBacklogs = tblWorkOrder.getFlowInfo().getJSONArray("handleUser").stream().map(item -> {
                TblWorkBacklog backlog = new TblWorkBacklog();
                backlog.setWorkId(workID);
                backlog.setHandleUser(Long.parseLong(item.toString()));
                backlog.setNodeId(nextNodeId);
                backlog.setIsCompletion(0);
                backlog.setFlowState(tblWorkOrder.getFlowState());
                return backlog;
            }).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(workBacklogs)){
                iTblWorkBacklogService.batchInsertTblWorkBacklog(workBacklogs);
            }
        }

        //保存通报信息
        int i = tblWorkOrderMapper.insertTblWorkOrder(tblWorkOrder);
        //保存通报对象信息
        List<JSONObject> reportTargetForm = tblWorkOrder.getReportTargetForm();
        handleReportTargetForm(reportTargetForm,tblWorkOrder);

        // 发送消息
        //tblMessageService.sendWorkMessage(tblWorkOrder);
        return i;
    }

    /**
     * 修改工单主表
     *
     * @param tblWorkOrder 工单主表
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateTblWorkOrder(TblWorkOrder tblWorkOrder) {
        Lock lock = lockPool.computeIfAbsent(tblWorkOrder.getId() != null? tblWorkOrder.getId().toString() : tblWorkOrder.getProdId(), id -> new ReentrantLock());
        lock.lock();
        try {
            // 工单状态为 待处理变更为待验证  待验证 根据验证是否通过将状态变更为 待处理 / 处理完成
        /*TblWorkBacklog backlog = new TblWorkBacklog();
        backlog.setWorkId(tblWorkOrder.getId());*/
            TblWorkOrder workOrderInDB = null;
            if(tblWorkOrder.getId() != null){
                workOrderInDB = this.selectTblWorkOrderById(tblWorkOrder.getId());
            }
            if(workOrderInDB == null && tblWorkOrder.getProdId() != null){
                workOrderInDB = this.selectTblWorkOrderById(Long.valueOf(tblWorkOrder.getProdId()));
            }
            if(tblWorkOrder.getId() == null && tblWorkOrder.getProdId() != null){
                if(workOrderInDB != null){
                    tblWorkOrder.setId(workOrderInDB.getId());
                    if(StrUtil.isBlank(tblWorkOrder.getWorkName())){
                        tblWorkOrder.setWorkName(workOrderInDB.getWorkName());
                    }
                    if(StrUtil.isBlank(tblWorkOrder.getWorkNo())){
                        tblWorkOrder.setWorkNo(workOrderInDB.getWorkNo());
                    }
                }
            }
            JSONObject flowNotify = null;
            List<TblWorkBacklog> notifyList = new ArrayList<>();
            boolean isTempSave = false; //是否临时保存
            Integer counterSign = 0; //审批方式 1=会签
            if(tblWorkOrder.getFlowInfo() != null){
                JSONObject flowInfo = tblWorkOrder.getFlowInfo();
                counterSign = flowInfo.getInteger("counterSign");
                counterSign = counterSign == null ? 0 : counterSign;
                JSONArray flowVariable = flowInfo.getJSONArray("flowVariable");
                flowNotify = flowInfo.getJSONObject("flowNotify");
                if(CollUtil.isNotEmpty(flowVariable)){
                    Object matchVariableObj = flowVariable.stream().filter(variable -> {
                        String jsonString = JSONObject.toJSONString(variable);
                        JSONObject variableJSON = JSONObject.parseObject(jsonString);
                        return "state".equals(variableJSON.getString("key"));
                    }).findFirst().orElse(null);
                    if(matchVariableObj != null){
                        String jsonString = JSONObject.toJSONString(matchVariableObj);
                        JSONObject matchVariableJSON = JSONObject.parseObject(jsonString);
                        if(StrUtil.isNotBlank(matchVariableJSON.getString("value"))){
                            tblWorkOrder.setFlowState(matchVariableJSON.getString("value"));
                        }
                    }
                }
                if(StrUtil.isBlank(tblWorkOrder.getFlowState())){
                    String thisStepId = flowInfo.getString("thisStepId");
                    if("end".equals(thisStepId)){
                        //节点结束
                        tblWorkOrder.setFlowState("4");
                    }
                }

                isTempSave = flowInfo.getBooleanValue("isTempSave",false);
            }

            AtomicBoolean nodeIsFinished = new AtomicBoolean(true);
            if(!isTempSave){
                iTblWorkBacklogService.deleteTblWorkBacklogByWorkId(tblWorkOrder.getId()); //删除待办
                /*if(counterSign != 1){
                    iTblWorkBacklogService.deleteTblWorkBacklogByWorkId(tblWorkOrder.getId()); //删除待办
                }else {
                    //会签
                    TblWorkBacklog query = new TblWorkBacklog();
                    query.setWorkId(tblWorkOrder.getId());
                    List<TblWorkBacklog> tblWorkBacklogs = iTblWorkBacklogService.selectTblWorkBacklogList(query);
                    int backlogCount = 0;
                    if(CollUtil.isEmpty(tblWorkBacklogs)){

                    }else {
                        backlogCount = tblWorkBacklogs.size();
                        List<TblWorkBacklog> matchList = tblWorkBacklogs.stream().filter(item -> Objects.equals(item.getHandleUser(), getUserId())).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(matchList)){
                            backlogCount -= matchList.size();
                            for (TblWorkBacklog workBacklog : matchList) {
                                iTblWorkBacklogService.deleteTblWorkBacklogByWorkAndUser(workBacklog.getWorkId(), workBacklog.getHandleUser());
                            }
                        }
                    }
                    if(backlogCount > 0){
                        nodeIsFinished = false;
                    }
                }*/
            }

            String thisHandleUser = null;
            JSONObject flowInfo = tblWorkOrder.getFlowInfo();
            List<TblWorkBacklog> saveBacklogList = new ArrayList<>();
            if(flowInfo != null){
                List<JSONObject> taskOperatorList = flowInfo.getList("taskOperatorList", JSONObject.class);
                if(CollUtil.isNotEmpty(taskOperatorList)){
                    log.info("taskOperatorList:{}", taskOperatorList);
                    taskOperatorList.forEach(taskOperator -> {
                        if(taskOperator.getInteger("completion") == 0){
                            TblWorkBacklog workBacklog = new TblWorkBacklog();
                            workBacklog.setWorkId(tblWorkOrder.getId());
                            workBacklog.setHandleUser(taskOperator.getLong("handleId"));
                            workBacklog.setIsCompletion(0);
                            workBacklog.setNodeId(taskOperator.getString("nodeCode"));
                            List<JSONObject> flowVariable = taskOperator.getList("currentFlowVariable", JSONObject.class);
                            if(CollUtil.isNotEmpty(flowVariable)){
                                flowVariable.stream().filter(variable -> "state".equals(variable.getString("key"))).findFirst()
                                        .ifPresent(variable -> workBacklog.setFlowState(variable.getString("value")));
                            }
                            saveBacklogList.add(workBacklog);
                            if(taskOperator.getBoolean("curNodeIsCompletion") != null && !taskOperator.getBoolean("curNodeIsCompletion")){
                                nodeIsFinished.set(false);
                            }
                        }
                    });
                }else {
                }
                if(StrUtil.isNotBlank(flowInfo.getString("thisHandleUser"))){
                    thisHandleUser = flowInfo.getString("thisHandleUser");
                }
                if(StrUtil.isNotBlank(flowInfo.getString("nodeProperties"))){
                    JSONObject nodeProperties = flowInfo.getJSONObject("nodeProperties");
                /*if(workOrderInDB != null && StrUtil.isNotBlank(workOrderInDB.getRemark2())){
                    JSONObject remark2Obj = JSONObject.parseObject(workOrderInDB.getRemark2());
                    JSONArray wordExport = remark2Obj.getJSONArray("wordExport");
                    if(wordExport != null){
                        JSONArray nodeWorkExport = nodeProperties.getJSONArray("wordExport");
                        JSONArray updateArr = new JSONArray();
                        updateArr.add(wordExport);
                        updateArr.add(nodeWorkExport);
                        nodeProperties.put("wordExport",updateArr);
                    }
                }*/
                    tblWorkOrder.setRemark2(nodeProperties.toJSONString());
                }
                //保存审核意见常用语
                String handleOpinion = flowInfo.getString("handleOpinion");
                if(StrUtil.isNotBlank(handleOpinion)){
                    TblWorkOrderCommonWords insertOrUpdateCommonWords = new TblWorkOrderCommonWords();
                    insertOrUpdateCommonWords.setContent(handleOpinion);
                    insertOrUpdateCommonWords.setType(1L);
                    insertOrUpdateCommonWords.setUserId(getUserId().toString());
                    workOrderCommonWordsService.insertOrUpdate(insertOrUpdateCommonWords);
                }
                //保存退回意见常用语
                String rejectHandleOpinion = flowInfo.getString("rejectHandleOpinion");
                if(StrUtil.isNotBlank(rejectHandleOpinion)){
                    TblWorkOrderCommonWords insertOrUpdateCommonWords = new TblWorkOrderCommonWords();
                    insertOrUpdateCommonWords.setContent(rejectHandleOpinion);
                    insertOrUpdateCommonWords.setType(2L);
                    insertOrUpdateCommonWords.setUserId(getUserId().toString());
                    workOrderCommonWordsService.insertOrUpdate(insertOrUpdateCommonWords);
                }
            }

            if(!nodeIsFinished.get()){
                tblWorkOrder.setFlowState(workOrderInDB != null ? workOrderInDB.getFlowState() : null);
            }

            if(!"4".equals(tblWorkOrder.getFlowState())){
                if("2".equals(tblWorkOrder.getFlowState())){
                    tblWorkOrder.setFeedbackDate(DateUtil.date());
                }
                boolean isStartNode = false;
                if(flowInfo != null){
                    isStartNode = flowInfo.getBooleanValue("isStartNode");
                    if(isStartNode){
                        tblWorkOrder.setFlowState("-1");
                        TblWorkHistory queryHistory = new TblWorkHistory();
                        queryHistory.setWorkId(tblWorkOrder.getId());
                        List<TblWorkHistory> tblWorkHistories = iTblWorkHistoryService.selectTblWorkHistoryList(queryHistory);
                        if(CollUtil.isNotEmpty(tblWorkHistories)){
                            TblWorkHistory matchHistory = tblWorkHistories.stream().filter(item -> "0".equals(item.getHandleState()) && (item.getType() == null || item.getType() != 2)).findFirst().orElse(null);
                            if(matchHistory != null){
                                thisHandleUser = matchHistory.getHandleUser();
                            }
                        }
                        //开始节点
                        if(workOrderInDB != null && StrUtil.isNotBlank(workOrderInDB.getRemark5())){
                            TblWorkBacklog startWorkBackLog = new TblWorkBacklog();
                            startWorkBackLog.setWorkId(tblWorkOrder.getId());
                            startWorkBackLog.setHandleUser(Long.valueOf(workOrderInDB.getRemark5()));
                            startWorkBackLog.setFlowState("-1");
                            saveBacklogList.add(startWorkBackLog);
                        }
                    }
                }

                if(StrUtil.isBlank(thisHandleUser)){
                    thisHandleUser = getUserId().toString();
                }

                //还未完成
                if(CollUtil.isNotEmpty(saveBacklogList)){
                    iTblWorkBacklogService.batchInsertTblWorkBacklog(saveBacklogList);
                    notifyList.addAll(saveBacklogList);
                }else {
                    /*TblWorkBacklog backlog = new TblWorkBacklog();
                    backlog.setWorkId(tblWorkOrder.getId());
                    if(isStartNode && StrUtil.isNotBlank(thisHandleUser)){
                        backlog.setHandleUser(Long.parseLong(thisHandleUser));
                    }
                    iTblWorkBacklogService.insertTblWorkBacklog(backlog);
                    notifyList.add(backlog);*/
                }
            }else {
                tblWorkOrder.setCompleteUser(thisHandleUser);
                tblWorkOrder.setCompleteTime(DateUtils.getNowDate());
            }

            TblWorkHistory history = new TblWorkHistory();
            history.setWorkId(tblWorkOrder.getId());
            history.setCreateBy(getUsername());
            history.setCreateTime(DateUtils.getNowDate());
            history.setHandleState(StrUtil.isNotBlank(tblWorkOrder.getFlowState())?tblWorkOrder.getFlowState():"0");
            history.setIsDel("0");
            history.setHandleUser(thisHandleUser);
            if(flowInfo != null){
                history.setNodeProperties(flowInfo.getString("nodeProperties"));
                if(flowInfo.getBoolean("isReject") != null && flowInfo.getBoolean("isReject")){
                    history.setNodeProperties("");
                }

                history.setNodeCode(flowInfo.getString("nodeCode"));

                //抄送
                String copyIds = flowInfo.getString("copyIds");
                if(StrUtil.isNotBlank(copyIds)){
                    String[] split = copyIds.split(",");
                    for (String copyUserId : split) {
                        TblWorkHistory copyHistory = new TblWorkHistory();
                        BeanUtil.copyProperties(history, copyHistory);
                        copyHistory.setHandleUser(copyUserId);
                        copyHistory.setType(2);
                        iTblWorkHistoryService.insertTblWorkHistory(copyHistory);
                    }
                }
            }
            if(!isTempSave){
                iTblWorkHistoryService.insertTblWorkHistory(history);
            }
            tblWorkOrder.setUpdateBy(getUsername());
            tblWorkOrder.setUpdateTime(DateUtils.getNowDate());

            String urlFiles = "";
            int updateTblWorkOrder = tblWorkOrderMapper.updateTblWorkOrder(tblWorkOrder);

            //通报对象处理 todo 在处置节点可能会出现A用户修改了B用户的信息的情况 目前想法是判断当前流程状态，如果是处置节点就不再修改其他用户的通报信息
            List<JSONObject> reportTargetForm = tblWorkOrder.getReportTargetForm();
            handleReportTargetForm(reportTargetForm,tblWorkOrder);

            if(isTempSave){
                return updateTblWorkOrder;
            }

            //通知
            if(flowNotify != null && flowNotify.getBoolean("enabled") != null && flowNotify.getBoolean("enabled") && nodeIsFinished.get()){
                this.sendFlowNotify(flowInfo, notifyList,tblWorkOrder);
            }

            if(tblWorkOrder.getId() != null && (CollUtil.isEmpty(tblWorkOrder.getEventIds()) || StrUtil.isBlank(tblWorkOrder.getWorkType()))){
                if(workOrderInDB != null){
                    if(CollUtil.isEmpty(tblWorkOrder.getEventIds())){
                        tblWorkOrder.setEventIds(workOrderInDB.getEventIds());
                    }
                    if(StrUtil.isBlank(tblWorkOrder.getWorkType())){
                        tblWorkOrder.setWorkType(workOrderInDB.getWorkType());
                    }
                }
            }

            if("4".equals(tblWorkOrder.getFlowState())){
                //将事件改为已处置
                List<JSONObject> eventDataList = new ArrayList<>();
                if(CollUtil.isNotEmpty(reportTargetForm)){
                    reportTargetForm.forEach(item -> {
                        List<JSONObject> formData = item.getList("formData", JSONObject.class);
                        if(CollUtil.isNotEmpty(formData)){
                            formData.forEach(formDataItem -> {
                                JSONObject eventData = formDataItem.getJSONObject("eventData");
                                if(eventData != null){
                                    eventData.keySet().forEach(key -> {
                                        List<JSONObject> eventItemList = eventData.getList(key, JSONObject.class);
                                        if(CollUtil.isNotEmpty(eventItemList)){
                                            eventItemList.forEach(eventItem -> {
                                                eventItem.put("handleUser", formDataItem.getString("handleUser"));
                                                eventDataList.add(eventItem);
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if(CollUtil.isNotEmpty(eventDataList)){
                    //按类型分类
                    for (int i = 0; i < 4; i++) {
                        //获取事件
                        int finalI = i;
                        List<JSONObject> matchList = eventDataList.stream().filter(item -> String.valueOf(finalI + 1).equals(item.getString("type"))).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(matchList)){
                            matchList.forEach(item -> {
                                String eventId = item.getString("eventId");
                                String type = item.getString("type");
                                Integer handleState = item.getInteger("handleState");
                                String eventHandleState = "1";
                                if(handleState != null && handleState == 2){
                                    eventHandleState = "2";
                                }
                                handleEvent(CollUtil.toList(item),type,eventHandleState,tblWorkOrder);
                            });
                        }
                    }
                }
            }else {
                //将事件改为处置中
                List<JSONObject> eventDataList = new ArrayList<>();
                if(CollUtil.isNotEmpty(reportTargetForm)){
                    reportTargetForm.forEach(item -> {
                        List<JSONObject> formData = item.getList("formData", JSONObject.class);
                        if(CollUtil.isNotEmpty(formData)){
                            formData.forEach(formDataItem -> {
                                JSONObject eventData = formDataItem.getJSONObject("eventData");
                                if(eventData != null){
                                    eventData.keySet().forEach(key -> {
                                        List<JSONObject> eventItemList = eventData.getList(key, JSONObject.class);
                                        if(CollUtil.isNotEmpty(eventItemList)){
                                            eventItemList.forEach(eventItem -> {
                                                eventItem.put("handleUser", formDataItem.getString("handleUser"));
                                                eventDataList.add(eventItem);
                                            });
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if(CollUtil.isNotEmpty(eventDataList)){
                    //按类型分类
                    for (int i = 0; i < 4; i++) {
                        //获取事件
                        int finalI = i;
                        List<JSONObject> matchList = eventDataList.stream().filter(item -> String.valueOf(finalI + 1).equals(item.getString("type"))).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(matchList)){
                            String workType = matchList.get(0).getString("type");
                            handleEvent(matchList,workType,"3",tblWorkOrder);
                        }
                    }
                }
            }
            return updateTblWorkOrder;
        }
        catch (Exception e){
            log.error("更新工单异常：{}",e.getMessage());
            e.printStackTrace();
            throw new ServiceException("更新工单异常");
        }
        finally {
            lock.unlock();
        }
    }

    private void handleEvent(List<JSONObject> eventDataList,String workType,String state,TblWorkOrder tblWorkOrder){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        SyncMessage<Object> syncMessage = new SyncMessage<>();
        syncMessage.setOperationType(OperationTypeEnum.INSERT);
        List<Object> syncDataList = new ArrayList<>();
        List<Long> eventList = eventDataList.stream().map(item -> item.getLong("eventId")).collect(Collectors.toList());
        if("1".equals(workType)){
            //IP漏洞
            List<MonitorBssVulnDeal> handleList = eventDataList.stream().map(item -> {
                MonitorBssVulnDeal monitorBssVulnDeal = new MonitorBssVulnDeal();
                monitorBssVulnDeal.setId(item.getLong("eventId"));
                monitorBssVulnDeal.setHandleState(state);
                monitorBssVulnDeal.setWorkOrderId(tblWorkOrder.getId());
                if ("1".equals(state) || "2".equals(state)) {
                    monitorBssVulnDeal.setDisposer(item.getString("handleUser"));
                    monitorBssVulnDeal.setHandleTime(new Date());
                    monitorBssVulnDeal.setHandleDesc(item.getString("handleDesc"));
                }
                return monitorBssVulnDeal;
            }).collect(Collectors.toList());
            monitorBssVulnDealService.batchUpdateHandleState(handleList);
            syncMessage.setDataType(DataTypeEnum.MONITOR_BSS_VULN_DEAL);
            List<MonitorBssVulnDeal> monitorBssVulnDeals = monitorBssVulnDealService.selectMonitorBssVulnDealByIds(eventList.toArray(new Long[0]));
            if(CollUtil.isNotEmpty(monitorBssVulnDeals)){
                monitorBssVulnDeals.forEach(item -> {
                    MonitorBssVulnDeal syncDataItem = new MonitorBssVulnDeal();
                    BeanUtil.copyProperties(item, syncDataItem);
                    syncDataItem.setHandleState(state);
                    syncDataList.add(syncDataItem);
                });
            }
        }else if ("2".equals(workType)){
            //web漏洞
            List<MonitorBssWebvulnDeal> handleList = eventDataList.stream().map(item -> {
                MonitorBssWebvulnDeal bean = new MonitorBssWebvulnDeal();
                bean.setId(item.getLong("eventId"));
                bean.setHandleState(Integer.valueOf(state));
                if ("1".equals(state) || "2".equals(state)) {
                    bean.setDisposer(item.getString("handleUser"));
                    bean.setHandleTime(new Date());
                    bean.setHandleDesc(item.getString("handleDesc"));
                }
                return bean;
            }).collect(Collectors.toList());
            monitorBssWebvulnDealService.batchUpdateHandleState(handleList);
            syncMessage.setDataType(DataTypeEnum.MONITOR_BSS_WEBVULN_DEAL);
            List<MonitorBssWebvulnDeal> monitorBssWebvulnDeals = monitorBssWebvulnDealService.selectMonitorBssWebvulnDealByIds(eventList.toArray(new Long[0]));
            if(CollUtil.isNotEmpty(monitorBssWebvulnDeals)){
                monitorBssWebvulnDeals.forEach(item -> {
                    MonitorBssWebvulnDeal syncDataItem = new MonitorBssWebvulnDeal();
                    BeanUtil.copyProperties(item, syncDataItem);
                    syncDataItem.setHandleState(Integer.parseInt(state));
                    syncDataList.add(syncDataItem);
                });
            }
        }else if("3".equals(workType)){
            //威胁告警
            List<TblThreatenAlarm> handleList = eventDataList.stream().map(item -> {
                TblThreatenAlarm bean = new TblThreatenAlarm();
                bean.setId(item.getLong("eventId"));
                bean.setHandleState(state);
                if ("1".equals(state) || "2".equals(state)) {
                    bean.setDisposer(item.getString("handleUser"));
                    bean.setWorkOrderId(tblWorkOrder.getId());
                    bean.setHandleDesc(item.getString("handleDesc"));
                }
                return bean;
            }).collect(Collectors.toList());
            threatenAlarmService.batchUpdateHandleState(handleList);
            syncMessage.setDataType(DataTypeEnum.BASE_THREATEN_ALARM);
            List<TblThreatenAlarm> threatenAlarms = threatenAlarmService.selectTblThreatenAlarmByIds(eventList.toArray(new Long[0]));
            if(CollUtil.isNotEmpty(threatenAlarms)){
                threatenAlarms.forEach(item -> {
                    TblThreatenAlarm syncDataItem = new TblThreatenAlarm();
                    BeanUtil.copyProperties(item, syncDataItem);
                    syncDataItem.setHandleState(state);
                    syncDataList.add(syncDataItem);
                });
            }
        }else if("4".equals(workType)){
            //弱口令事件
            List<MonitorBssWpDeal> handleList = eventDataList.stream().map(item -> {
                MonitorBssWpDeal bean = new MonitorBssWpDeal();
                bean.setId(item.getLong("eventId"));
                bean.setHandleState(Integer.valueOf(state));
                if ("1".equals(state) || "2".equals(state)) {
                    bean.setDisposer(item.getString("handleUser"));
                    bean.setHandleTime(new Date());
                    bean.setHandleDesc(item.getString("handleDesc"));
                }
                return bean;
            }).collect(Collectors.toList());
            monitorBssWpDealService.batchUpdateHandleState(handleList);
            syncMessage.setDataType(DataTypeEnum.MONITOR_BSS_WP_DEAL);
            List<MonitorBssWpDeal> monitorBssWpDeals = monitorBssWpDealService.selectMonitorBssWpDealByIds(eventList.toArray(new Long[0]));
            if(CollUtil.isNotEmpty(monitorBssWpDeals)){
                monitorBssWpDeals.forEach(item -> {
                    MonitorBssWpDeal syncDataItem = new MonitorBssWpDeal();
                    BeanUtil.copyProperties(item, syncDataItem);
                    syncDataItem.setHandleState(Integer.valueOf(state));
                    syncDataList.add(syncDataItem);
                });
            }
        }
        if(CollUtil.isNotEmpty(syncDataList)){
            syncDataList.forEach(syncDataItem -> {
                syncMessage.setData(syncDataItem);
                syncMessage.setTimestamp(System.currentTimeMillis());
                handleDataSyncSender.sendDataSync(syncMessage);
            });
        }
    }

    /**
     * 流程通知
     */
    private void sendFlowNotify(JSONObject flowInfo,List<TblWorkBacklog> notifyList,TblWorkOrder tblWorkOrder){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ThreadUtil.execute(() -> {
            JSONObject flowNotify = flowInfo.getJSONObject("flowNotify");
            if(flowNotify == null || flowNotify.getBoolean("enabled") == null){
                return;
            }
            List<JSONObject> smsDataList = new ArrayList<>();
            //流程通知
            if(CollUtil.isNotEmpty(notifyList) && CollUtil.isNotEmpty(flowNotify.getJSONArray("types"))){
                List<Long> types = flowNotify.getJSONArray("types").stream().map(typeItem -> Long.parseLong(typeItem.toString())).collect(Collectors.toList());
                TblWorkOrder tblWorkOrderInDB = tblWorkOrderMapper.selectTblWorkOrderById(tblWorkOrder.getId());
                List<SysUser> userList = userService.selectUserByIds(notifyList.stream().map(TblWorkBacklog::getHandleUser).collect(Collectors.toList()));
                types.forEach(type -> {
                    if(type == FlowNotifyType.INTERIOR_MESSAGE.getCode()){
                        //站内信
                        notifyList.forEach(backlog -> {
                            //通知公告
                            SysNotice sysNotice = new SysNotice();
                            // 设置接收对象为特定用户
                            sysNotice.setAcceptType("user");
                            sysNotice.setAcceptIds("[" + backlog.getHandleUser() + "]");
                            sysNotice.setNoticeTitle("您有待处理工单(" + tblWorkOrderInDB.getWorkName() + "),请及时处理");
                            sysNotice.setNoticeType("2");
                            sysNotice.setCreateTime(DateUtil.date());
                            sysNotice.setCreateBy(loginUser.getUsername());
                            sysNotice.setBusinessId(tblWorkOrderInDB.getId());
                            sysNotice.setStatus("0");
                            sysNoticeService.insertNotice(sysNotice);
                            log.info("工单发送站内信通知:{},{}",backlog.getHandleUser(),sysNotice.getNoticeTitle());
                        });
                    }
                    if(type == FlowNotifyType.SMS.getCode()){
                        //短信
                        String smsTemplate = flowNotify.getString("smsTemplate");
                        Integer smsTemplateType = flowNotify.getInteger("smsTemplateType");
                        if(StrUtil.isNotBlank(smsTemplate)){
                            JSONObject smsData = new JSONObject();
                            smsData.put("userList",userList);
                            smsData.put("smsTemplate", smsTemplate);
                            smsData.put("smsTemplateType", smsTemplateType);
                            smsDataList.add(smsData);
                        }
                    }
                });
            }

            //抄送通知
            Boolean isReject = flowInfo.getBoolean("isReject");
            if(isReject == null || !isReject){
                String smsTemplate = flowInfo.getString("copySmsTemplate");
                Integer smsTemplateType = flowInfo.getInteger("copySmsTemplateType");
                String copyIds = flowInfo.getString("copyIds");
                if(StrUtil.isNotBlank(smsTemplate) && StrUtil.isNotBlank(copyIds)){
                    List<Long> copyUserIds = StrUtil.split(copyIds, ",").stream().map(Long::parseLong).collect(Collectors.toList());
                    List<SysUser> userList = userService.selectUserByIds(CollUtil.distinct(copyUserIds));
                    if(CollUtil.isNotEmpty(userList)){
                        JSONObject smsData = new JSONObject();
                        smsData.put("userList",userList);
                        smsData.put("smsTemplate", smsTemplate);
                        smsData.put("smsTemplateType", smsTemplateType);
                        smsDataList.add(smsData);
                    }
                }
            }

            //执行通知
            if(CollUtil.isNotEmpty(smsDataList)){
                JSONObject fillData = getFillData(tblWorkOrder);
                fillData.put("nowDate",DateUtil.format(DateUtil.date(),"yyyy年MM月dd日"));
                tblMessageService.sendWorkMessage(fillData, smsDataList, loginUser);
            }
        });
    }

    /**
     * 删除工单主表信息
     *
     * @param id 工单主表主键
     * @return 结果
     */
    @Override
    public int deleteTblWorkOrderById(Long id) {
        return tblWorkOrderMapper.deleteTblWorkOrderById(id);
    }

    /**
     * 批量删除工单主表
     *
     * @param ids 需要删除的工单主表主键
     * @return 结果
     */
    @Override
    public int deleteTblWorkOrderByIds(Long[] ids) {
        return tblWorkOrderMapper.deleteTblWorkOrderByIds(ids);
    }

    @Override
    public List<TblWorkOrder> selectWaitList(TblWorkOrder tblWorkOrder) {
        handlePrem(tblWorkOrder);
        /*tblWorkOrder.setWaitDoneBy(SecurityUtils.getUserId());
        tblWorkOrder.setFlowHandleUser(SecurityUtils.getUserId().toString());*/
        List<TblWorkOrder> tblWorkOrders = tblWorkOrderMapper.selectWaitList(tblWorkOrder);
        for (TblWorkOrder workOrder : tblWorkOrders) {
            //附件
            getFileUrls(workOrder);
        }
        if(CollUtil.isNotEmpty(tblWorkOrders)){
            tblWorkOrders.forEach(workOrder -> {
                List<String> historyNodeProperties = workOrder.getHistoryNodeProperties();
                if(historyNodeProperties == null){
                    historyNodeProperties = new ArrayList<>();
                }
                if(StrUtil.isNotBlank(workOrder.getNodeProperties())){
                    historyNodeProperties.add(workOrder.getNodeProperties());
                }
                if(CollUtil.isNotEmpty(historyNodeProperties)){
                    //处理历史节点属性
                    List<JSONObject> wordExportNodes = handleWordExportNodeProperties(historyNodeProperties);
                    if(CollUtil.isNotEmpty(wordExportNodes)){
                        String nodeProperties = workOrder.getNodeProperties();
                        if(StrUtil.isNotBlank(nodeProperties)){
                            JSONObject nodePropObj = JSONObject.parseObject(nodeProperties);
                            nodePropObj.put("wordExport", wordExportNodes);
                            nodeProperties = nodePropObj.toJSONString();
                        }
                        workOrder.setNodeProperties(nodeProperties);
                    }
                }
            });
        }
        return tblWorkOrders;
    }

    @Override
    public List<TblWorkOrder> selectHaveDoneList(TblWorkOrder tblWorkOrder) {
        //tblWorkOrder.setFlowHandleUser(SecurityUtils.getUserId().toString());
        handlePrem(tblWorkOrder);
        return tblWorkOrderMapper.selectHaveDoneList(tblWorkOrder);
    }

    @Override
    public List<TblWorkOrder> getBuildWaitList(TblWorkOrder tblWorkOrder) {
        List<TblWorkOrder> list = selectWaitList(tblWorkOrder);
        return list;
    }

    @Override
    public List<TblWorkOrder> getFinishList(TblWorkOrder tblWorkOrder) {
        List<TblWorkOrder> list = selectHaveDoneList(tblWorkOrder);
        /*list.forEach(e -> {
            e.setIsOver("已超时");
            if (e.getComplateTime().getTime() >= new Date().getTime()) {
                e.setIsOver("未超时");
            }
            if (e.getWorkType().equals("0")) {
                switch (e.getHandleStatus()) {
                    case "0":
                        e.setHandleStatus("完成修复");
                        break;
                    case "1":
                        e.setHandleStatus("误报");
                        break;
                    case "2":
                        e.setHandleStatus("忽略");
                        break;
                }
            } else {
                switch (e.getHandleStatus()) {
                    case "0":
                        e.setHandleStatus("ip封禁");
                        break;
                    case "1":
                        e.setHandleStatus("白名单");
                        break;
                    case "2":
                        e.setHandleStatus("忽略");
                        break;
                }
            }
        });*/
        return list;
    }

    @Override
    public List<TblWorkOrder> selectList(TblWorkOrder tblWorkOrder) {
        handlePrem(tblWorkOrder);
        List<TblWorkOrder> tblWorkOrders = tblWorkOrderMapper.selectList(tblWorkOrder);
        List<String> eventIds1 = new ArrayList<>();
        List<String> eventIds2 = new ArrayList<>();
        List<String> eventIds3 = new ArrayList<>();
        tblWorkOrders.forEach(item -> {
            if(CollUtil.isNotEmpty(item.getEventIds()) && StrUtil.isNotBlank(item.getWorkType())){
                if("1".equals(item.getWorkType())){
                    //IP漏洞
                    eventIds1.addAll(item.getEventIds());
                }
                if("2".equals(item.getWorkType())){
                    //Web漏洞
                    eventIds2.addAll(item.getEventIds());
                }
                if("3".equals(item.getWorkType())){
                    //威胁事件
                    eventIds3.addAll(item.getEventIds());
                }
            }
        });
        if(CollUtil.isNotEmpty(eventIds1)){
            List<MonitorBssVulnDeal> monitorBssVulnDeals = monitorBssVulnDealService.selectMonitorBssVulnDealByIds(
                    CollUtil.distinct(eventIds1).stream().map(Long::valueOf).toArray(Long[]::new));
            tblWorkOrders.forEach(item -> {
                if(!"1".equals(item.getWorkType()) || CollUtil.isEmpty(item.getEventIds())){
                    return;
                }
                List<String> eventIds = item.getEventIds();
                List<MonitorBssVulnDeal> matchList = monitorBssVulnDeals.stream().filter(deal -> eventIds.contains(deal.getId().toString())).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(matchList)){
                    item.setEventNameList(matchList.stream().map(MonitorBssVulnDeal::getTitle).collect(Collectors.toList()));
                }
            });
        }
        fillReportForm(tblWorkOrders);
        return tblWorkOrders;
    }

    @Override
    public WorkOrderStatistics getWorkOrderEfficiency() {
        return tblWorkOrderMapper.selectWorkOrderEfficiency(new Date());
    }

    @Override
    public JSONObject getStatisticsData(TblWorkOrder workOrder) {
        handlePrem(workOrder);
        JSONObject statisticsData = tblWorkOrderMapper.getStatisticsData(workOrder);
        int allCount = 0;
        for (String key : statisticsData.keySet()) {
            allCount += statisticsData.getIntValue(key,0);
        }
        statisticsData.put("allCount",allCount);
        return statisticsData;
    }

    @Override
    public int count() {
        return tblWorkOrderMapper.count();
    }

    @Override
    public void readNotify(String id) {
        List<SysNotice> noticeList = sysNoticeService.selectNoticeByBusinessAndAcceptId(id,SecurityUtils.getUserId());
        if(CollUtil.isNotEmpty(noticeList)){
            Long userId = SecurityUtils.getUserId();
            // 过滤出正常状态且为待办通知类型的通知，然后标记为已读
            noticeList.stream()
                .filter(notice -> "0".equals(notice.getStatus()) && "2".equals(notice.getNoticeType()))
                .forEach(notice -> {
                    // 使用新的阅读状态管理方式标记为已读
                    sysNoticeService.markNoticeAsRead(notice.getNoticeId(), userId);
                });
        }
    }
    @Override
    public void sendNoticeInfo() {
        SysNotice notice = new SysNotice();
        notice.setUserId(SecurityUtils.getUserId());
        notice.setNoticeType("2");
        notice.setStatus("0");
        List<SysNotice> list = sysNoticeService.selectNoticeList(notice);
        if (list.size() > 15) {
            WebSocketService.sendMessage(String.valueOf(SecurityUtils.getUserId()), "您有" + list.size() + "条待办通知需要处理！");
        } else {
            list.forEach(this::sendNoticeInfo);
        }
    }

    @Override
    public void sendNoticeInfo(SysNotice notice) {
        // 获取通知的接收人用户ID列表
        List<Long> userIds = sysNoticeService.getAcceptUserIds(notice.getAcceptType(), notice.getAcceptIds());

        // 向每个接收人发送WebSocket消息
        for (Long userId : userIds) {
            WebSocketService.sendMessage(String.valueOf(userId), notice.getNoticeTitle());
        }
    }

    private void handlePrem(TblWorkOrder workOrder){
        LoginUser loginUser = SecurityUtils.getLoginUser();
        //查看自己需要处置的
        workOrder.setFlowHandleUser(loginUser.getUserId().toString());

        if(loginUser.getUser().isAdmin()){
            //超管 看全部
            workOrder.setQueryAll(true);
            return;
        }
        SysUser user = loginUser.getUser();
        List<SysRole> roles = user.getRoles();
        if(CollUtil.isEmpty(roles)){
            //没有角色 只看自己
            workOrder.setCreateBy(loginUser.getUserId().toString());
            workOrder.setFlowHandleUser(user.getUserId().toString());
            workOrder.setOnlySelf(true);
            return;
        }
        boolean anyMatch = roles.stream().anyMatch(role -> "1".equals(role.getDataScope()));
        if(anyMatch){
            //有全部数据权限 看全部
            workOrder.setQueryAll(true);
            return;
        }
        workOrder.setCreateBy(loginUser.getUserId().toString());
        List<Long> customRoleIds = new ArrayList<>(); //自定义权限角色
        List<Long> deptTreeIds = new ArrayList<>(); //部门及子部门权限
        List<Long> deptIds = new ArrayList<>(); //部门权限
        roles.forEach(role -> {
            if("2".equals(role.getDataScope())){
                //自定义权限
                customRoleIds.add(role.getRoleId());
            }else if("3".equals(role.getDataScope())){
                //本部门权限
                deptIds.add(user.getDeptId());
            }else if("4".equals(role.getDataScope())){
                //本部门及以下权限
                deptTreeIds.add(user.getDeptId());
            }else if("5".equals(role.getDataScope())){
                //仅本人
                workOrder.setFlowHandleUser(user.getUserId().toString());
                workOrder.setOnlySelf(true);
            }
        });
        if(CollUtil.isNotEmpty(customRoleIds)){
            //查询自定义权限角色
            Page<Object> localPage = PageUtils.getLocalPage();
            if(localPage != null){
                PageUtils.clearPage();
            }
            List<SysRoleDept> roleDeptList = roleDeptMapper.selectListByRoleIds(CollUtil.distinct(customRoleIds));
            if(localPage != null){
                PageUtils.startPage();
            }
            if(CollUtil.isNotEmpty(roleDeptList)){
                roleDeptList.forEach(item -> deptIds.add(item.getDeptId()));
            }
        }
        if(CollUtil.isNotEmpty(deptTreeIds)){
            //查询本部门及以下部门
            Page<Object> localPage = PageUtils.getLocalPage();
            if(localPage != null){
                PageUtils.clearPage();
            }
            List<SysDept> deptList = deptService.selectDeptAndChildrenByIds(deptTreeIds);
            if(localPage != null){
                PageUtils.startPage();
            }
            if(CollUtil.isNotEmpty(deptList)){
                deptList.forEach(item -> deptIds.add(item.getDeptId()));
            }
        }

        workOrder.setHandleDeptIds(CollUtil.distinct(deptIds));
    }

    private List<JSONObject> handleWordExportNodeProperties(List<String> properties){
        List<JSONObject> wordExportItemList = new ArrayList<>(properties.size());
        properties.forEach(item -> {
            if(StrUtil.isNotBlank(item)){
                JSONObject prop = JSONObject.parseObject(item);
                JSONArray wordExport = prop.getJSONArray("wordExport");
                if(CollUtil.isNotEmpty(wordExport)){
                    wordExportItemList.addAll(wordExport.toList(JSONObject.class));
                }
            }
        });
        if(CollUtil.isNotEmpty(wordExportItemList)){
            return CollUtil.distinct(wordExportItemList);
        }
        return null;
    }

    @Override
    public TblWorkOrder getDetails(String id) {
        return tblWorkOrderMapper.getDetails(id);
    }

    @Override
    public int countThreatenNotificationNum(OverviewParams params) {
        return tblWorkOrderMapper.countThreatenNotificationNum(params);
    }

    @Override
    public int countWorkOrderNumByStatus(TblWorkOrder tblWorkOrder) {
        return tblWorkOrderMapper.countWorkOrderNumByStatus(tblWorkOrder);
    }

    @Override
    public JSONObject getFillData(TblWorkOrder tblWorkOrder) {
        JSONObject docData = cn.hutool.core.bean.BeanUtil.toBean(tblWorkOrder, JSONObject.class);
        //期号
        int count = this.count();
        docData.put("issue", count);
        //发现时间
        if (!Objects.isNull(tblWorkOrder.getEventCreateTime())) {
            docData.put("eventCreateTime",DateUtil.format(tblWorkOrder.getEventCreateTime(),"yyyy年M月d日"));
        }
        DateTime nowDate = DateUtil.date();
        docData.put("nowDate",DateUtil.format(nowDate,"yyyy年M月d日"));
        if(tblWorkOrder.getCreateTime() != null){
            docData.put("createTimeStr",DateUtil.format(tblWorkOrder.getCreateTime(),"yyyy年M月d日"));
        }
        //反馈时间
        if(tblWorkOrder.getFeedbackDate() != null){
            docData.put("feedbackDate",DateUtil.format(tblWorkOrder.getFeedbackDate(),"yyyy年M月d日"));
        }
        //附件名称
        if(StrUtil.isNotBlank(tblWorkOrder.getDescribeFileUrl())){
            String describeFileUrl = tblWorkOrder.getDescribeFileUrl();
            List<String> fileList = StrUtil.split(describeFileUrl, "|");
            List<String> fileNameList = fileList.stream().map(this::getFileUrlName).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(fileNameList)){
                docData.put("describeFileName",CollUtil.join(fileNameList,"，"));
                docData.put("describeFileName1",fileNameList.get(0));
                StringBuilder resStr = new StringBuilder();
                if(fileNameList.size() > 1){
                    for (int i = 0; i < fileNameList.size(); i++) {
                        if(i == 0){
                            resStr.append(fileNameList.get(i));
                        }else {
                            resStr.append("\n").append(fileNameList.get(i));
                        }
                    }
                    docData.put("describeFileNameExt",resStr.toString());
                }
            }
        }
        if(StrUtil.isNotBlank(tblWorkOrder.getFeedbackFileUrl())){
            String feedbackFileUrl = tblWorkOrder.getFeedbackFileUrl();
            List<String> fileList = StrUtil.split(feedbackFileUrl, "|");
            List<String> fileNameList = fileList.stream().map(this::getFileUrlName).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(fileNameList)){
                docData.put("feedbackFileName",CollUtil.join(fileNameList,"，"));
                docData.put("feedbackFileName1",fileNameList.get(0));
                StringBuilder resStr = new StringBuilder();
                if(fileNameList.size() > 1){
                    for (int i = 0; i < fileNameList.size(); i++) {
                        if(i == 0){
                            resStr.append(fileNameList.get(i));
                        }else {
                            resStr.append("\n").append(fileNameList.get(i));
                        }
                    }
                    docData.put("feedbackFileNameExt",resStr.toString());
                }
            }
        }
        //处置部门
        if(tblWorkOrder.getHandleUser() != null){
            //获取用户
            SysUser sysUser = userService.selectUserById(tblWorkOrder.getHandleUser());
            if(sysUser != null && sysUser.getDept() != null){
                String ancestors = sysUser.getDept().getAncestors();
                if(StrUtil.isNotBlank(ancestors)){
                    List<String> deptIds = StrUtil.split(ancestors, ",");
                    if(CollUtil.isNotEmpty(deptIds)){
                        String deptId = "100";
                        if(deptIds.size() > 1){
                            deptId = deptIds.get(1);
                        }
                        SysDept sysDept = deptService.selectDeptById(Long.valueOf(deptId));
                        if(sysDept != null){
                            docData.put("handleUserCity", sysDept.getDeptName());
                        }
                    }
                }
            }
        }

        //加入当前处置部门
        if(tblWorkOrder.getCurTargetDept() != null){
            tblWorkOrder.setHandleDept(tblWorkOrder.getCurTargetDept());
        }
        //全路径处置部门
        if(tblWorkOrder.getHandleDept() != null){
            SysDept handleDept = deptService.selectDeptById(tblWorkOrder.getHandleDept());
            if(handleDept != null){
                String ancestors = handleDept.getAncestors();
                if(StrUtil.isNotBlank(ancestors)){
                    List<Long> deptIds = new ArrayList<>();
                    deptIds.add(tblWorkOrder.getHandleDept());
                    deptIds.addAll(StrUtil.split(ancestors, ",").stream().map(Long::valueOf).collect(Collectors.toList()));

                    List<SysDept> deptList = deptService.selectDeptByIds(deptIds);
                    if(CollUtil.isNotEmpty(deptList)){
                        //排序
                        List<SysDept> deptListSort = new ArrayList<>();
                        deptIds.forEach(deptId -> {
                            deptList.stream().filter(dept -> dept.getDeptId().equals(deptId)).findFirst().ifPresent(deptListSort::add);
                        });
                        for (int i = 0; i < deptListSort.size(); i++) {
                            SysDept thisDept = deptListSort.get(i);
                            docData.put("handleDept" + (i + 1), thisDept.getDeptName());
                        }
                        docData.put("handleDeptAllStr",CollUtil.join(deptListSort.stream().map(SysDept::getDeptName).collect(Collectors.toList()),""));
                        if(deptListSort.size() < 2){
                            docData.put("handleDeptAllIgnoreStr",docData.getString("handleDeptAllStr"));
                        }else {
                            //排除顶级部门
                            docData.put("handleDeptAllIgnoreStr",CollUtil.join(deptListSort.stream().filter(dept -> dept.getDeptId() != 100L).map(SysDept::getDeptName).collect(Collectors.toList()),""));
                        }

                        //排除掉顶级部门后看看是否还有省厅、省公安厅字样
                        boolean anyMatch = deptList.stream().anyMatch(dept -> dept.getDeptId() != 100L && (dept.getDeptName().contains("省厅") || dept.getDeptName().contains("省公安厅")));
                        String publicSecurityName = "公安局";
                        if(anyMatch){
                            publicSecurityName = "公安厅";
                        }
                        docData.put("publicSecurityName", publicSecurityName);
                    }
                }
            }
        }

        //提取各节点处理人
        List<TblWorkHistory> historyList = iTblWorkHistoryService.selectWorkHistoryByWorkId(tblWorkOrder.getId());
        if(CollUtil.isNotEmpty(historyList)){
            List<Long> handleUserIdList = new ArrayList<>();
            List<List<TblWorkHistory>> historyGroupList = CollUtil.groupByField(historyList, "nodeCode");
            historyGroupList.forEach(groupItem -> {
                //取最后创建时间
                TblWorkHistory last = groupItem.get(groupItem.size() - 1);
                String handleUser = last.getHandleUser();
                handleUserIdList.add(Long.valueOf(handleUser));
            });
            if(CollUtil.isNotEmpty(handleUserIdList)){
                List<SysUser> userList = userService.selectUserByIds(handleUserIdList);
                if(CollUtil.isNotEmpty(userList)){
                    for (int i = 0; i < handleUserIdList.size(); i++) {
                        docData.put("handleUser"+(i+1), "");
                        Long currentUserId = handleUserIdList.get(i);
                        SysUser sysUser = userList.stream().filter(user -> user.getUserId().equals(currentUserId)).findFirst().orElse(null);
                        if(sysUser != null){
                            docData.put("handleUser"+(i+1), sysUser.getNickName());
                        }
                    }
                }
            }
        }

        //当前处理人部门信息
        try {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if(loginUser != null){
                Long thisDeptId = loginUser.getDeptId();
                if(thisDeptId != null){
                    SysDept thisDept = deptService.selectDeptById(thisDeptId);
                    if(thisDept != null){
                        String ancestors = thisDept.getAncestors();
                        if(StrUtil.isNotBlank(ancestors)){
                            List<Long> deptIds = StrUtil.split(ancestors, ",").stream().map(Long::valueOf).collect(Collectors.toList());
                            deptIds.add(tblWorkOrder.getHandleDept());
                            List<SysDept> deptList = deptService.selectDeptByIds(deptIds);
                            if(CollUtil.isNotEmpty(deptList)){
                                //排序
                                List<SysDept> deptListSort = new ArrayList<>();
                                deptIds.forEach(deptId -> {
                                    deptList.stream().filter(dept -> dept.getDeptId().equals(deptId)).findFirst().ifPresent(deptListSort::add);
                                });
                                for (int i = 0; i < deptListSort.size(); i++) {
                                    SysDept dept = deptListSort.get(i);
                                    docData.put("thisDept" + (i + 1), dept.getDeptName());
                                }
                                docData.put("thisDeptAllStr",CollUtil.join(deptListSort.stream().map(SysDept::getDeptName).collect(Collectors.toList()),""));
                                if(deptListSort.size() < 2){
                                    docData.put("thisDeptAllIgnoreStr",docData.getString("thisDeptAllStr"));
                                }else {
                                    //排除顶级部门
                                    docData.put("thisDeptAllIgnoreStr",CollUtil.join(deptListSort.stream().filter(dept -> dept.getDeptId() != 100L).map(SysDept::getDeptName).collect(Collectors.toList()),""));
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception ignore){

        }

        //紧急程度
        if(tblWorkOrder.getUrgency() != null){
            docData.put("urgencyName", getDictDataLabel("work_order_urgency",String.valueOf(tblWorkOrder.getUrgency())));
        }
        //是否公开
        if(StrUtil.isNotBlank(tblWorkOrder.getIsPublic())){
            docData.put("isPublicName", getDictDataLabel("work_order_public",tblWorkOrder.getIsPublic()));
        }
        //严重程度
        if(StrUtil.isNotBlank(tblWorkOrder.getSeverityLevel())){
            docData.put("severityLevelName", "严重程度："+getDictDataLabel("work_order_severity_level",tblWorkOrder.getSeverityLevel()));
        }
        //通报类型
        if(StrUtil.isNotBlank(tblWorkOrder.getRemark6())){
            docData.put("remark6Name", getDictDataLabel("work_order_report_type",tblWorkOrder.getRemark6()));
        }
        //通报时间
        if(tblWorkOrder.getReportDate() != null){
            docData.put("reportDateStr",DateUtil.format(tblWorkOrder.getReportDate(),"yyyy年MM月dd日"));
        }
        //期号
        if(tblWorkOrder.getPeriod() != null){
            docData.put("period",String.valueOf(tblWorkOrder.getPeriod()));
        }
        //流程状态
        if(tblWorkOrder.getFlowState() == null){
            tblWorkOrder.setFlowState("2"); //默认待审核
        }
        if(tblWorkOrder.getFlowState() != null){
            docData.put("flowStateName", flowStateMap.get(tblWorkOrder.getFlowState()));
        }
        docData.put("nowYear",String.valueOf(DateUtil.year(nowDate)));
        return docData;
    }

    private String getDictDataLabel(String dictType,String dictValue){
        SysDictData queryDictData = new SysDictData();
        queryDictData.setDictType(dictType);
        List<SysDictData> dictDataList = sysDictDataService.selectDictDataList(queryDictData);
        for (SysDictData dictData : dictDataList) {
            if(dictValue.equals(dictData.getDictValue())){
                return dictData.getDictLabel();
            }
        }
        return null;
    }

    private String getFileUrlName(String fileUrl){
        String last1 = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
        return last1.substring(last1.indexOf("-") + 1).split("\\.")[0];
    }

    private void handleReportTargetForm(List<JSONObject> reportTargetForm,TblWorkOrder tblWorkOrder){
        if(CollUtil.isNotEmpty(reportTargetForm)){
            List<TblWorkOrderDept> workOrderDeptList = new ArrayList<>(reportTargetForm.size());
            List<TblWorkOrderTarget> workOrderTargetList = new ArrayList<>();
            List<TblWorkOrderEvent> workOrderEventList = new ArrayList<>();
            reportTargetForm.forEach(reportTarget -> {
                Long deptId = reportTarget.getLong("deptId");
                Long workOrderDeptId = reportTarget.getLong("id");
                if(workOrderDeptId == null){
                    workOrderDeptId = snowflake.nextId();
                }
                TblWorkOrderDept workOrderDept = new TblWorkOrderDept();
                workOrderDept.setDeptId(deptId);
                workOrderDept.setWorkOrderId(tblWorkOrder.getId());
                workOrderDept.setId(workOrderDeptId);
                workOrderDeptList.add(workOrderDept);
                //通报对象
                List<JSONObject> formData = reportTarget.getList("formData", JSONObject.class);
                if(CollUtil.isNotEmpty(formData)){
                    formData.forEach(formDataItem -> {
                        Long handleDept = formDataItem.getLong("handleDept");
                        Long handleUser = formDataItem.getLong("handleUser");
                        Long applicationId = formDataItem.getLong("applicationId");
                        String loginUrl = formDataItem.getString("loginUrl");
                        Long manager = formDataItem.getLong("manager");
                        String phone = formDataItem.getString("phone");
                        Long formDataItemId = formDataItem.getLong("id");
                        if(formDataItemId == null){
                            formDataItemId = snowflake.nextId();
                        }
                        TblWorkOrderTarget workOrderTarget = new TblWorkOrderTarget();
                        workOrderTarget.setId(formDataItemId);
                        workOrderTarget.setWorkOrderId(tblWorkOrder.getId());
                        workOrderTarget.setWorkOrderDeptId(workOrderDept.getId());
                        workOrderTarget.setHandleDept(handleDept);
                        workOrderTarget.setHandleUser(handleUser);
                        workOrderTarget.setApplicationId(applicationId);
                        workOrderTarget.setLoginUrl(loginUrl);
                        workOrderTarget.setManager(manager);
                        workOrderTarget.setPhone(phone);
                        //workOrderTarget.setHandleStatus(0L); //未处置
                        workOrderTargetList.add(workOrderTarget);
                        //通报事件
                        JSONObject eventData = formDataItem.getJSONObject("eventData");
                        if(CollUtil.isNotEmpty(eventData)){
                            eventData.forEach((key, value) -> {
                                if(value != null){
                                    List<Object> eventDataItems = (List<Object>) value;
                                    if(CollUtil.isNotEmpty(eventDataItems)){
                                        eventDataItems.forEach(eventDataItem -> {
                                            JSONObject eventItem = cn.hutool.core.bean.BeanUtil.toBean(eventDataItem, JSONObject.class);
                                            TblWorkOrderEvent workOrderEvent = new TblWorkOrderEvent();
                                            workOrderEvent.setId(eventItem.getLong("id"));
                                            workOrderEvent.setWorkOrderId(tblWorkOrder.getId());
                                            workOrderEvent.setWorkOrderTargetId(workOrderTarget.getId());
                                            workOrderEvent.setType(workEventTypeMap.get(key));
                                            workOrderEvent.setEventId(eventItem.getLong("eventId"));
                                            workOrderEvent.setHandleState(eventItem.getLong("handleState"));
                                            workOrderEvent.setHandleDesc(eventItem.getString("handleDesc"));
                                            workOrderEvent.setHandleFile(eventItem.getString("handleFile"));
                                            workOrderEvent.setCheckDesc(eventItem.getString("checkDesc"));
                                            workOrderEvent.setCheckFile(eventItem.getString("checkFile"));
                                            workOrderEvent.setCheckState(eventItem.getInteger("checkState"));
                                            //workOrderEvent.setHandleState(0L); //未处置
                                            workOrderEventList.add(workOrderEvent);
                                        });
                                    }
                                }
                            });
                        }
                    });
                }
            });
            //新增、更新、删除
            if(CollUtil.isEmpty(workOrderDeptList)){
                if(Objects.equals(tblWorkOrder.getFlowState(), "-1")){
                    //为空 删除所有
                    workOrderDeptService.deleteTblWorkOrderDeptByWorkId(tblWorkOrder.getId());
                }
            }else {
                //不为空 新增或删除
                List<TblWorkOrderDept> saveList = new ArrayList<>();
                List<TblWorkOrderDept> deleteList = new ArrayList<>();
                //查询已有数据
                TblWorkOrderDept queryWorkOrderDept = new TblWorkOrderDept();
                queryWorkOrderDept.setWorkOrderId(tblWorkOrder.getId());
                List<TblWorkOrderDept> workOrderDeptInDbList = workOrderDeptService.selectTblWorkOrderDeptList(queryWorkOrderDept);
                if(CollUtil.isEmpty(workOrderDeptInDbList)){
                    //没有已存在数据 全部新增
                    saveList.addAll(workOrderDeptList);
                }else {
                    if(Objects.equals(tblWorkOrder.getFlowState(), "-1")){
                        //已有数据存在但新数据不存在的删除，已有数据不存在但新数据存在的新增
                        workOrderDeptInDbList.forEach(workOrderDeptInDb -> {
                            if(workOrderDeptList.stream().noneMatch(workOrderDept -> workOrderDept.getId().equals(workOrderDeptInDb.getId()))){
                                //新数据不存在
                                deleteList.add(workOrderDeptInDb);
                            }
                        });
                    }
                    workOrderDeptList.forEach(workOrderDept -> {
                        if(workOrderDeptInDbList.stream().noneMatch(workOrderDeptInDb -> workOrderDeptInDb.getDeptId().equals(workOrderDept.getDeptId()))){
                            //没有已存在数据
                            saveList.add(workOrderDept);
                        }
                    });
                }
                if(CollUtil.isNotEmpty(saveList)){
                    //批量新增
                    workOrderDeptService.batchInsert(saveList);
                }
                if(CollUtil.isNotEmpty(deleteList)){
                    //批量删除
                    workOrderDeptService.deleteTblWorkOrderDeptByIds(deleteList.stream().map(TblWorkOrderDept::getId).toArray(Long[]::new));
                }
            }
            if(CollUtil.isEmpty(workOrderTargetList)){
                if(Objects.equals(tblWorkOrder.getFlowState(), "-1")){
                    //为空 删除所有
                    workOrderTargetService.deleteTblWorkOrderTargetByWorkId(tblWorkOrder.getId());
                }
            }else {
                //不为空 新增或更新或删除
                List<TblWorkOrderTarget> saveList = new ArrayList<>();
                List<TblWorkOrderTarget> updateList = new ArrayList<>();
                List<TblWorkOrderTarget> deleteList = new ArrayList<>();
                //查询已有数据
                TblWorkOrderTarget queryWorkOrderTarget = new TblWorkOrderTarget();
                queryWorkOrderTarget.setWorkOrderId(tblWorkOrder.getId());
                List<TblWorkOrderTarget> workOrderTargetInDbList = workOrderTargetService.selectTblWorkOrderTargetList(queryWorkOrderTarget);
                if(CollUtil.isEmpty(workOrderTargetInDbList)){
                    //没有已存在数据 添加
                    saveList.addAll(workOrderTargetList);
                }else {
                    //已有数据存在但新数据不存在的删除，已有数据不存在但新数据存在的新增，都存在的更新
                    workOrderTargetInDbList.forEach(workOrderTargetInDb -> {
                        if(workOrderTargetList.stream().noneMatch(workOrderTarget -> workOrderTarget.getId().equals(workOrderTargetInDb.getId()))){
                            if(Objects.equals(tblWorkOrder.getFlowState(), "-1")){
                                //新数据不存在
                                deleteList.add(workOrderTargetInDb);
                            }
                        }else {
                            //新数据存在,更新
                            workOrderTargetList.stream().filter(workOrderTarget1 -> workOrderTarget1.getId().equals(workOrderTargetInDb.getId())).findFirst().ifPresent(updateList::add);
                        }
                    });
                    workOrderTargetList.forEach(workOrderTarget -> {
                        if(workOrderTargetInDbList.stream().noneMatch(workOrderTargetInDb -> workOrderTargetInDb.getId().equals(workOrderTarget.getId()))){
                            //没有已存在数据
                            saveList.add(workOrderTarget);
                        }
                    });
                }
                if(CollUtil.isNotEmpty(saveList)){
                    //批量新增
                    workOrderTargetService.batchInsert(saveList);
                }
                if(CollUtil.isNotEmpty(updateList)){
                    //批量更新
                    workOrderTargetService.batchUpdate(updateList);
                }
                if(CollUtil.isNotEmpty(deleteList)){
                    //批量删除
                    workOrderTargetService.deleteTblWorkOrderTargetByIds(deleteList.stream().map(TblWorkOrderTarget::getId).toArray(Long[]::new));
                }
            }
            if(CollUtil.isEmpty(workOrderEventList)){
                //为空 删除所有
                if(Objects.equals(tblWorkOrder.getFlowState(), "-1")){
                    workOrderEventService.deleteTblWorkOrderEventByWorkId(tblWorkOrder.getId());
                }
            }else {
                //不为空 新增或更新或删除
                List<TblWorkOrderEvent> saveList = new ArrayList<>();
                List<TblWorkOrderEvent> updateList = new ArrayList<>();
                List<TblWorkOrderEvent> deleteList = new ArrayList<>();
                //查询已有数据
                TblWorkOrderEvent queryWorkOrderEvent = new TblWorkOrderEvent();
                queryWorkOrderEvent.setWorkOrderId(tblWorkOrder.getId());
                List<TblWorkOrderEvent> workOrderEventInDbList = workOrderEventService.selectTblWorkOrderEventList(queryWorkOrderEvent);
                if(CollUtil.isEmpty(workOrderEventInDbList)){
                    //没有已存在数据 添加
                    saveList.addAll(workOrderEventList);
                }else {
                    //已有数据存在但新数据不存在的删除，已有数据不存在但新数据存在的新增，都存在的更新
                    workOrderEventInDbList.forEach(workOrderEventInDb -> {
                        if(workOrderEventList.stream().noneMatch(workOrderEvent -> workOrderEventInDb.getId().equals(workOrderEvent.getId()))){
                            if(Objects.equals(tblWorkOrder.getFlowState(), "-1")){
                                //新数据不存在
                                deleteList.add(workOrderEventInDb);
                            }
                        }else {
                            //新数据存在,更新
                            workOrderEventList.stream().filter(workOrderEvent1 -> workOrderEvent1.getId().equals(workOrderEventInDb.getId())).findFirst().ifPresent(updateList::add);
                        }
                    });
                    workOrderEventList.forEach(workOrderEvent -> {
                        if(workOrderEventInDbList.stream().noneMatch(workOrderEventInDb -> workOrderEventInDb.getId().equals(workOrderEvent.getId()))){
                            //没有已存在数据
                            saveList.add(workOrderEvent);
                        }
                    });
                }

                if (CollUtil.isNotEmpty(saveList)){
                    //批量新增
                    workOrderEventService.batchInsert(saveList);
                }
                if (CollUtil.isNotEmpty(updateList)){
                    //批量更新
                    workOrderEventService.batchUpdate(updateList);
                }
                if (CollUtil.isNotEmpty(deleteList)){
                    workOrderEventService.deleteTblWorkOrderEventByIds(deleteList.stream().map(TblWorkOrderEvent::getId).toArray(Long[]::new));
                }
            }
        }
    }

    public void fillReportForm(List<TblWorkOrder> tblWorkOrderList){
        if(CollUtil.isNotEmpty(tblWorkOrderList)){
            List<Long> wordOrderIds = tblWorkOrderList.stream().map(TblWorkOrder::getId).collect(Collectors.toList());
            TblWorkOrderDept queryWorkOrderDept = new TblWorkOrderDept();
            queryWorkOrderDept.setWorkOrderIds(wordOrderIds);
            List<TblWorkOrderDept> allWorkOrderDeptList = workOrderDeptService.selectTblWorkOrderDeptList(queryWorkOrderDept);
            if(CollUtil.isEmpty(allWorkOrderDeptList)){
                return;
            }
            List<Long> workOrderDeptIds = allWorkOrderDeptList.stream().map(TblWorkOrderDept::getId).collect(Collectors.toList());
            TblWorkOrderTarget queryWorkOrderTarget = new TblWorkOrderTarget();
            queryWorkOrderTarget.setWorkOrderDeptIds(workOrderDeptIds);
            List<TblWorkOrderTarget> allWorkOrderTargetList = workOrderTargetService.selectTblWorkOrderTargetList(queryWorkOrderTarget);

            TblWorkOrderEvent queryWorkOrderEvent = new TblWorkOrderEvent();
            queryWorkOrderEvent.setWorkOrderIds(wordOrderIds);
            List<TblWorkOrderEvent> allWorkOrderEventList = workOrderEventService.selectTblWorkOrderEventList(queryWorkOrderEvent);

            tblWorkOrderList.forEach(tblWorkOrder -> {
                List<TblWorkOrderDept> workOrderDeptList = allWorkOrderDeptList.stream().filter(workOrderDept -> workOrderDept.getWorkOrderId().equals(tblWorkOrder.getId())).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(workOrderDeptList)){
                    List<JSONObject> reportTargetList = workOrderDeptList.stream().map(workOrderDept -> {
                        JSONObject workOrderDeptObj = new JSONObject();
                        workOrderDeptObj.put("id", workOrderDept.getId());
                        workOrderDeptObj.put("deptId", workOrderDept.getDeptId());
                        workOrderDeptObj.put("deptName", workOrderDept.getDeptName());
                        //查询关联的通报对象
                        List<TblWorkOrderTarget> workOrderTargetList = allWorkOrderTargetList.stream().filter(workOrderTarget -> workOrderTarget.getWorkOrderDeptId().equals(workOrderDept.getId())).collect(Collectors.toList());
                        if(CollUtil.isNotEmpty(workOrderTargetList)){
                            List<JSONObject> formDataList = workOrderTargetList.stream().map(workOrderTarget -> {
                                JSONObject workOrderTargetObj = JSONObject.parseObject(JSON.toJSONString(workOrderTarget));
                                //查询关联的事件
                                List<TblWorkOrderEvent> workOrderEventList = allWorkOrderEventList.stream().filter(orderEvent -> orderEvent.getWorkOrderTargetId().equals(workOrderTarget.getId())).collect(Collectors.toList());
                                if(CollUtil.isNotEmpty(workOrderEventList)){
                                    List<List<TblWorkOrderEvent>> workOrderEventGroupList = CollUtil.groupByField(workOrderEventList, "type");
                                    JSONObject workOrderEventObj = new JSONObject();
                                    workOrderEventGroupList.forEach(workOrderEventGroup -> {
                                        List<JSONObject> eventArr = workOrderEventGroup.stream().map(groupItem -> {
                                            JSONObject jsonObject = new JSONObject();
                                            jsonObject.put("eventId", groupItem.getEventId());
                                            jsonObject.put("type", groupItem.getType());
                                            jsonObject.put("handleState", groupItem.getHandleState());
                                            jsonObject.put("id", groupItem.getId());
                                            return jsonObject;
                                        }).collect(Collectors.toList());
                                        workOrderEventObj.put("type" + workOrderEventGroup.get(0).getType(), eventArr);
                                    });
                                    workOrderTargetObj.put("eventData", workOrderEventObj);
                                }
                                return workOrderTargetObj;
                            }).collect(Collectors.toList());
                            workOrderDeptObj.put("formData", formDataList);
                        }
                        return workOrderDeptObj;
                    }).collect(Collectors.toList());
                    tblWorkOrder.setReportTargetForm(reportTargetList);
                }
            });
        }
    }

    @Override
    public List<TblWorkOrderTarget> getTargetList(TblWorkOrder workOrder) {
        List<TblWorkOrderTarget> result = new ArrayList<>();
        if(workOrder == null || CollUtil.isEmpty(workOrder.getReportTargetForm())){
            return result;
        }
        workOrder.getReportTargetForm().forEach(workOrderDept -> {
            result.addAll(workOrderDept.getList("formData",TblWorkOrderTarget.class));
        });
        return result;
    }

    @Override
    public List<TblWorkOrderEvent> getEventList(TblWorkOrder workOrder,TblWorkOrderTarget workTarget) {
        List<TblWorkOrderEvent> result = new ArrayList<>();
        if(workOrder == null || CollUtil.isEmpty(workOrder.getReportTargetForm())){
            return result;
        }
        workOrder.getReportTargetForm().forEach(workOrderDept -> {
            List<JSONObject> formData = workOrderDept.getList("formData", JSONObject.class);
            formData.forEach(workOrderTarget -> {
                Long curTargetId = workOrderTarget.getLong("id");
                if(Objects.equals(workTarget.getId(), curTargetId)){
                    JSONObject eventData = workOrderTarget.getJSONObject("eventData");
                    if(eventData != null){
                        eventData.keySet().forEach(typeKey -> {
                            List<TblWorkOrderEvent> eventDataList = eventData.getList(typeKey, TblWorkOrderEvent.class);
                            result.addAll(eventDataList);
                        });
                    }
                }
            });
        });
        return result;
    }

    public static void main(String[] args) throws  FileNotFoundException {
        DocumentConverter.create()
                .input(new FileInputStream("C:\\Users\\<USER>\\Desktop\\新建文件夹\\src.docx"))
                .output(new FileOutputStream("C:\\Users\\<USER>\\Desktop\\新建文件夹\\222.pdf"))
                .convert(DocumentConvertType.PDF);
    }
}

