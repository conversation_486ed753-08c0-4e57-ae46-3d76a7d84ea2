<template>
  <div class="custom-container">
    <div class="custom-content-container-right">
      <div class="custom-content-search-box">
        <el-form
          v-show="showSearch"
          ref="queryForm"
          :model="queryParams"
          size="small"
          :inline="true"
          label-position="right"
          label-width="70px"
        >
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="入口分类" prop="entranceCategory">
                <el-select
                  v-model="queryParams.entranceCategory"
                  placeholder="请选择入口分类"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.entrance_category_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="入口名称" prop="entranceName">
                <el-input
                  v-model="queryParams.entranceName"
                  placeholder="请输入入口名称"
                  clearable
                  style="width: 240px"
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="queryParams.status"
                  placeholder="入口状态"
                  clearable
                  style="width: 240px"
                >
                  <el-option
                    v-for="dict in dict.type.sys_normal_disable"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item class="custom-search-btn">
                <el-button class="btn1" size="small" @click="handleQuery">查询</el-button>
                <el-button class="btn2" size="small" @click="resetQuery">重置</el-button>
                <el-button v-if="!showAll" class="btn2" size="small" icon="el-icon-arrow-down" @click="showAll=true">
                  展开
                </el-button>
                <el-button v-else class="btn2" size="small" icon="el-icon-arrow-up" @click="showAll=false">收起
                </el-button>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-show="showAll" :gutter="10">
            <el-col :span="6">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="dateRange"
                  style="width: 240px"
                  value-format="yyyy-MM-dd"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="custom-content-container">
        <div class="common-header">
          <div><span class="common-head-title">快捷入口管理列表</span></div>
          <div class="common-head-right">
            <el-row :gutter="10">
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:entrance:add']"
                  type="primary"
                  size="small"
                  @click="handleAdd"
                >新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:entrance:remove']"
                  size="small"
                  class="btn1"
                  :disabled="multiple"
                  @click="handleDelete"
                >批量删除</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button
                  v-hasPermi="['system:entrance:export']"
                  size="small"
                  class="btn1"
                  @click="handleExport"
                >导出</el-button>
              </el-col>
            </el-row>
          </div>
        </div>
        <el-table v-loading="loading" height="100%" :data="entranceList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column label="入口分类" prop="entranceCategory" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.entrance_category_type" :value="scope.row.entranceCategory" />
            </template>
          </el-table-column>
          <el-table-column label="入口名称" prop="entranceName" :show-overflow-tooltip="true" />
          <el-table-column label="关联菜单" prop="menuName" :show-overflow-tooltip="true" />
          <el-table-column label="入口图标" prop="iconUrl" width="100">
            <template slot-scope="scope">
              <el-image
                v-if="scope.row.iconUrl"
                :src="scope.row.iconUrl"
                fit="contain"
                style="width: 40px; height: 40px;"
                :preview-src-list="[scope.row.iconUrl]"
              >
                <div slot="error" class="image-slot">
                  <i class="el-icon-picture-outline" />
                </div>
              </el-image>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-value="0"
                inactive-value="1"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right" :show-overflow-tooltip="false" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button
                v-hasPermi="['system:entrance:edit']"
                size="mini"
                type="text"
                @click="handleUpdate(scope.row)"
              >编辑</el-button>
              <el-button
                v-hasPermi="['system:entrance:remove']"
                size="mini"
                type="text"
                class="table-delBtn"
                @click="handleDelete(scope.row)"
              >删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </div>

    <!-- 添加或修改快捷入口管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="入口分类" prop="entranceCategory">
          <el-select v-model="form.entranceCategory" placeholder="请选择入口分类">
            <el-option
              v-for="dict in dict.type.entrance_category_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="关联菜单" prop="menuId">
          <!--          <el-input-number v-model="form.menuId" controls-position="right" :min="0" />-->
          <select-tree
            v-model="form.menuId"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
              disabled: function(data, node) {
                return data.children && data.children.length > 0;
              }
            }"
            :options="menuOptions"
            :clearable="true"
            :accordion="true"
            placeholder="请选择关联菜单"
          />
        </el-form-item>
        <el-form-item label="入口名称" prop="entranceName">
          <el-input v-model="form.entranceName" placeholder="请输入入口名称" />
        </el-form-item>
        <el-form-item label="入口图标" prop="iconUrl">
          <file-upload
            ref="iconUpload"
            v-model="form.iconUrl"
            :limit="1"
            :file-size="1"
            :file-type="['png', 'jpg', 'jpeg']"
            :dis-upload="false"
          />
        </el-form-item>
        <el-form-item v-if="form.iconUrl" label="图标预览">
          <div class="icon-preview">
            <el-image
              :src="form.iconUrl"
              fit="contain"
              style="width: 60px; height: 60px; border: 1px solid #ddd; border-radius: 4px;"
            >
              <div slot="error" class="image-slot">
                <i class="el-icon-picture-outline" />
              </div>
            </el-image>
          </div>
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEntrance, getEntrance, delEntrance, addEntrance, updateEntrance } from '@/api/system/entrance'
import FileUpload from '@/components/FileUpload'
import { getAllTreeselect } from '@/api/system/notice'
import SelectTree from '@/views/system/notice/CustomSelectTree.vue'
export default {
  name: 'Entrance',
  components: {
    SelectTree,
    FileUpload
  },
  dicts: ['entrance_category_type', 'sys_normal_disable'],
  data() {
    return {
      showAll: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 快捷入口管理表格数据
      entranceList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entranceCategory: undefined,
        entranceName: undefined,
        status: undefined
      },
      // 菜单列表
      menuOptions: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entranceCategory: [
          { required: true, message: '入口分类不能为空', trigger: 'change' }
        ],
        entranceName: [
          { required: true, message: '入口名称不能为空', trigger: 'blur' }
        ],
        menuId: [
          { required: true, message: '关联菜单不能为空', trigger: 'blur' }
        ],
        iconUrl: [
          { required: true, message: '入口图标不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getMenuTreeselect()
  },
  methods: {

    /** 查询快捷入口管理列表 */
    getList() {
      this.loading = true
      listEntrance(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.entranceList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询菜单树结构 */
    getMenuTreeselect() {
      getAllTreeselect().then(response => {
        const menuOptions = response.data.map((item, index) => {
          if (index === 0 && item.children) {
            const { children, ...rest } = item
            return children[0];
          }
          return item
        })
        this.menuOptions = menuOptions.map(item => {
          // 递归处理函数
          const transformNode = (node) => {
            return {
              label: node.meta.title,
              value: node.meta.menuId || node.path,
              children: node.children ? node.children.map(transformNode) : undefined
            }
          }
          return transformNode(item)
        })
      })
    },
    // 入口状态修改
    handleStatusChange(row) {
      const text = row.status === '0' ? '启用' : '停用'
      this.$modal.confirm('确认要"' + text + '""' + row.entranceName + '"入口吗？').then(() => {
        const data = {
          entranceId: row.entranceId,
          status: row.status
        }
        return updateEntrance(data)
      }).then(() => {
        this.$modal.msgSuccess(text + '成功')
      }).catch(() => {
        row.status = row.status === '0' ? '1' : '0'
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        entranceId: undefined,
        entranceCategory: undefined,
        entranceName: undefined,
        menuId: undefined,
        iconUrl: undefined,
        status: '0',
        createBy: undefined,
        createTime: undefined,
        updateBy: undefined,
        updateTime: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.entranceId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加快捷入口管理'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const entranceId = row.entranceId || this.ids
      getEntrance(entranceId).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改快捷入口管理'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.entranceId != null) {
            updateEntrance(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addEntrance(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const entranceIds = row.entranceId || this.ids
      this.$modal.confirm('是否确认删除快捷入口管理编号为"' + entranceIds + '"的数据项？').then(function() {
        return delEntrance(entranceIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/entrance/export', {
        ...this.queryParams
      }, `entrance_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

<style scoped>
.icon-preview {
  display: flex;
  align-items: center;
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style>
